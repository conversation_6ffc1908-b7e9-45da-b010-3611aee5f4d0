import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Restangular } from 'ngx-restangular';
import { AuthService } from 'src/app/core/services/auth.service';
import {orderBy} from 'lodash';
import { RedirectService } from 'src/app/core/services/redirect.service';
import { SnackBarComponent } from 'src/app/shared/components/snack-bar/snack-bar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { map } from 'rxjs/operators';
import { SimpleHistoryComponent } from 'src/app/shared/components/simple-history/simple-history.component';
import { MatDialog } from '@angular/material/dialog';
import { DialogData } from 'src/app/shared/components/dialog/DialogData';
import { DialogComponent } from 'src/app/shared/components/dialog/dialog.component';
import { NgxObjectDiffService } from 'ngx-object-diff';

const CIA_AEREA_CLEAN = {
  ciaAerea: [null],
  nacInt: [null],
  percentualRav: [0],  
  valorMinimoRav: [0], 
  moeda: [null],
  variavel: [false],   
  exceptions: [[]],    
  exceptionsControl: [null],  
  blockId: [null]    
};

const CIA_AEREA_VALIDATORS = {
  ciaAerea: [Validators.required],
  nacInt: [Validators.required],
  percentualRav: [Validators.required],
  valorMinimoRav: [Validators.required],
  moeda: [Validators.required]
};

@Component({
  selector: 'app-ravs-form',
  templateUrl: './ravs-form.component.html',
  styleUrls: ['./ravs-form.component.scss'],
})

export class RavsFormComponent implements OnInit {
  public get isNew(): boolean {
    return !(this.currentId !== null && typeof this.currentId !== 'undefined');
  }

  public get isActive(): boolean {
    return this.active;
  }

  get ciasAereasFormArray() {
    return (this.ravFormGroup.controls.ciasAereas as FormArray);
  }

  public loading: boolean = true;
  public active: boolean = undefined;
  public title: string;
  loadingSearch = false;

  private _showBulkModal: boolean = false;

  public get showBulkModal(): boolean {
    return this._showBulkModal;
  }

  public set showBulkModal(value: boolean) {
    console.log(`showBulkModal mudando de ${this._showBulkModal} para ${value}`);
    if (!value && this._showBulkModal) {
      console.log('Modal sendo fechado! Stack trace:', new Error().stack);
    }
    this._showBulkModal = value;
  }
  public bulkFormGroup: FormGroup;
  public selectedCias: any[] = [];
  public editingBlockId: string | null = null;
  public ciasDisponiveis: any[] = [];
  public statusDisponiveisPorCia: Map<string, string[]> = new Map();
  public statusOptionsForCurrentCia: any[] = []; 
  public statusOptionsByFormIndex: Map<number, any[]> = new Map(); 
  public bulkCiasEndpointKey: string = 'ciasAereas/combo'; 

  protected readonly RESOURCE_NAME: string = "configuracoesrav";
  protected readonly RAVS: string = "ravs";
  private currentId: number = undefined;
  private initialized: boolean = false;
  public readonly ciaAerea = CIA_AEREA_CLEAN;
  ravFormGroup: FormGroup;


  data = {
    ativo: null,
    ciasAereas: [],
    editavel: null,
    empresas: [],
    grupos: [],
    historico: [],
    id: null,
    idEditavel: null,
    idOrigem: null,
    nacInt: null,
    nome: null,
    percentualRav: null,
    prioridade: null,
    produtos: [],
    statusPublicacao: null,
    tipoConfigRav: null,
    tipoTarifaAcordo: null,
    valorMinimo: null,
    versao: null,
  };

  constructor(
    private _formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private _router: Router,
    private restangular: Restangular,
    private authService: AuthService,
    private _snackBar: MatSnackBar,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private objectDiff: NgxObjectDiffService,
    private redirectService: RedirectService) {

  }
  public formValue2Value(data: any): any {
    data.percentualRav = this.data.percentualRav ? this.data.percentualRav : 0;
    data.editavel = this.data.editavel ? this.data.editavel : true;
    data.ciasAereas = this.changeObj();
    return data;
  }

  private changeObj(){
    var cias = this.ravFormGroup.getRawValue().ciasAereas;
    return cias
      .filter(cia => cia.ciaAerea && cia.ciaAerea.id !== null && cia.ciaAerea.id !== undefined) 
      .map(cia => {
        const result: any = {
          id: cia.ciaAerea.id,
          nome: cia.ciaAerea.nome,
          codigo: cia.ciaAerea.codigo,
          nacInt: cia.nacInt,
          percentualRav: cia.percentualRav / 100,
          valorMinimoRav: cia.valorMinimoRav,
          moeda: cia.moeda,
          variavel: cia.variavel ? cia.variavel : false,
        };


        if (cia.ciaAerea && cia.ciaAerea.id === 0 && cia.exceptions && cia.exceptions.length > 0) {
          result.exceptions = cia.exceptions.map((exc: any) => exc.nome).join(',');
        }


        if (cia.blockId && cia.blockId.trim() !== '') {
          result.blockId = cia.blockId;
        }

        return result;
      });
  }

  private async getCiasDisponiveis(): Promise<any[]> {
    try {
      const ravId = this.data?.id;
      if (!ravId) {
        return [];
      }

      const response = await this.restangular.one('configuracoesrav/rf', ravId).one('cias-disponiveis').get();
      return response || [];
    } catch (error) {
      console.error('Erro ao buscar cias disponíveis:', error);
      return [];
    }
  }

  private async getStatusDisponiveis(codigoCia: string): Promise<any[]> {
    try {
      const ravId = this.data?.id;
      if (!ravId || !codigoCia) {
        return ['Nacional', 'Internacional', 'Ambos'];
      }

      console.log(`Buscando status disponíveis para cia ${codigoCia} no RAV ${ravId}`);

      const response = await this.restangular.one('configuracoesrav/rf', ravId).one('status-disponiveis', codigoCia).get().toPromise();

      console.log('Response completa do getStatusDisponiveis:', response);
      console.log('Tipo da response:', typeof response);
      console.log('É array?', Array.isArray(response));

      let statusDisponiveis;
      if (response && response.body) {
        statusDisponiveis = response.body;
      } else if (Array.isArray(response)) {
        statusDisponiveis = response;
      } else {
        statusDisponiveis = response;
      }

      if (Array.isArray(statusDisponiveis)) {
        return statusDisponiveis;
      } else {
        console.warn('Response não é um array válido, usando fallback. Response:', response);
        return [
          {id: 'NAC', nome: 'Nacional'},
          {id: 'INT', nome: 'Internacional'},
          {id: 'AMBOS', nome: 'Ambos'}
        ];
      }
    } catch (error) {
      console.error('Erro ao buscar status disponíveis:', error);
      return ['Nacional', 'Internacional', 'Ambos'];
    }
  }

  public isTodaAsCiasSelected(form: AbstractControl): boolean {
    const ciaAerea = form.get('ciaAerea')?.value;
    return ciaAerea && ciaAerea.id !== null && ciaAerea.id !== undefined && ciaAerea.id === 0;
  }

  public getExceptionsControl(form: AbstractControl): AbstractControl {
    return form.get('exceptionsControl');
  }

  public getExceptions(form: AbstractControl): any[] {
    return form.get('exceptions')?.value || [];
  }

  public async onCiaAereaSelectionChange(selectedCia: any, form: AbstractControl, formIndex?: number): Promise<void> {
    form.get('exceptions')?.setValue([]);
    form.get('exceptionsControl')?.setValue(null);

    console.log('Cia aérea selecionada:', selectedCia, 'para form index:', formIndex);

    if (selectedCia && selectedCia.id === 0) {
      console.log('Selecionou "Todas as cias", habilitando exceções');
      return;
    }

    if (selectedCia && selectedCia.codigo) {
      if (!this.data?.id) {
        const allStatusOptions = [
          {id: 'NAC', nome: 'Nacional'},
          {id: 'INT', nome: 'Internacional'},
          {id: 'AMBOS', nome: 'Ambos'}
        ];

        if (formIndex !== undefined) {
          this.statusOptionsByFormIndex.set(formIndex, allStatusOptions);
        }

        console.log('RAV novo, mostrando todas as opções de status para index:', formIndex);
      } else {
        try {
          const statusDisponiveis = await this.getStatusDisponiveis(selectedCia.codigo);
          console.log('Status disponíveis para', selectedCia.codigo, ':', statusDisponiveis);

          this.updateStatusOptionsForSpecificForm(form, statusDisponiveis, formIndex);

          this.statusDisponiveisPorCia.set(selectedCia.codigo, statusDisponiveis);
        } catch (error) {
          console.error('Erro ao buscar status disponíveis:', error);
        }
      }
    } else {
      if (formIndex !== undefined) {
        this.statusOptionsByFormIndex.set(formIndex, []);
      }
    }
  }

  private updateStatusOptions(form: AbstractControl, statusDisponiveis: any[]): void {
    this.statusOptionsForCurrentCia = statusDisponiveis;

    console.log('Status disponíveis do backend (método legado):', statusDisponiveis);
    console.log('Opções de status definidas (método legado):', this.statusOptionsForCurrentCia);

    const currentStatus = form.get('nacInt')?.value;
    const availableCodes = this.statusOptionsForCurrentCia.map(opt => opt.id);
    if (currentStatus && !availableCodes.includes(currentStatus)) {
      form.get('nacInt')?.setValue(null);
      console.log('Status atual não disponível, campo limpo (método legado)');
    }
  }


  private updateStatusOptionsForSpecificForm(form: AbstractControl, statusDisponiveis: any[], formIndex?: number): void {
    console.log('Atualizando status para form index:', formIndex, 'com opções:', statusDisponiveis);

    if (formIndex !== undefined) {
      this.statusOptionsByFormIndex.set(formIndex, statusDisponiveis);
      console.log('Opções armazenadas para index', formIndex, ':', statusDisponiveis);
    }

    const currentStatus = form.get('nacInt')?.value;
    const availableCodes = statusDisponiveis.map(opt => opt.id);
    if (currentStatus && !availableCodes.includes(currentStatus)) {
      form.get('nacInt')?.setValue(null);
      console.log('Status atual não disponível para index', formIndex, ', campo limpo');
    }
  }

  private async populateStatusOptionsForExistingData(): Promise<void> {
    console.log('Populando opções de status para dados existentes');

    const allStatusOptions = [
      {id: 'NAC', nome: 'Nacional'},
      {id: 'INT', nome: 'Internacional'},
      {id: 'AMBOS', nome: 'Ambos'}
    ];

    const ciasAereasArray = this.ravFormGroup.get('ciasAereas') as FormArray;
    for (let i = 0; i < ciasAereasArray.length; i++) {
      this.statusOptionsByFormIndex.set(i, allStatusOptions);
      console.log('Opções de status definidas para linha existente', i, ':', allStatusOptions);
    }

    this.statusOptionsForCurrentCia = allStatusOptions;

    console.log('Opções de status definidas para todos os dados existentes');
  }


  public getStatusOptionsForFormIndex(formIndex: number): any[] {
    const options = this.statusOptionsByFormIndex.get(formIndex);
    if (options && options.length > 0) {
      return options;
    }

    return this.statusOptionsForCurrentCia || [];
  }

  public getCiasEndpoint(): string {
    const ravId = this.data?.id;
    if (ravId) {
      return `configuracoesrav/rf/${ravId}/cias-disponiveis`;
    } else {
      return 'ciasAereas/combo';
    }
  }

  public getBulkCiasEndpoint(): string {
    const ravId = this.data?.id;
    const selectedStatus = this.bulkFormGroup?.get('nacInt')?.value;

    console.log('getBulkCiasEndpoint chamado:', { ravId, selectedStatus });

    if (!selectedStatus) {
      console.log('Sem status selecionado, não carregando cias');
      return null;
    }

    const statusId = typeof selectedStatus === 'object' ? selectedStatus.id : selectedStatus;

    if (ravId && statusId) {
      const endpoint = `configuracoesrav/rf/${ravId}/cias-por-status/${statusId}`;
      console.log('Usando endpoint filtrado:', endpoint);
      return endpoint;
    } else {
      console.log('Usando endpoint padrão: ciasAereas/combo');
      return 'ciasAereas/combo';
    }
  }

  public getBulkCiasEndpointWithStatus(statusId: string): string {
    const ravId = this.data?.id;

    console.log('getBulkCiasEndpointWithStatus chamado:', { ravId, statusId });

    if (ravId && statusId) {
      const endpoint = `configuracoesrav/rf/${ravId}/cias-por-status/${statusId}`;
      console.log('Usando endpoint filtrado:', endpoint);
      return endpoint;
    } else {
      console.log('Usando endpoint padrão: ciasAereas/combo');
      return 'ciasAereas/combo';
    }
  }

  public onExceptionSelectionChange(selectedCia: any, form: AbstractControl): void {
    if (selectedCia && selectedCia.id !== 0) {
      const currentExceptions = this.getExceptions(form);

      const alreadyExists = currentExceptions.some(exception => exception.id === selectedCia.id);

      if (!alreadyExists) {
        const newExceptions = [...currentExceptions, selectedCia];
        form.get('exceptions')?.setValue(newExceptions);
      }

      form.get('exceptionsControl')?.setValue(null);
    }
  }

  public removeException(exceptionToRemove: any, form: AbstractControl): void {
    const currentExceptions = this.getExceptions(form);
    const filteredExceptions = currentExceptions.filter(exception => exception.nome !== exceptionToRemove.nome);
    form.get('exceptions')?.setValue(filteredExceptions);

    console.log('Removendo exceção:', exceptionToRemove.nome);
    console.log('Exceções antes:', currentExceptions.map(e => e.nome));
    console.log('Exceções depois:', filteredExceptions.map(e => e.nome));
  }

  public addCiaAerea(): void {
    try {
      console.log('addCiaAerea: Iniciando...');

      const newFormGroup = this._formBuilder.group(CIA_AEREA_CLEAN);
      console.log('addCiaAerea: FormGroup criado:', newFormGroup);

      console.log('addCiaAerea: ciasAereasFormArray antes:', this.ciasAereasFormArray.length);
      this.ciasAereasFormArray.push(newFormGroup);
      console.log('addCiaAerea: ciasAereasFormArray depois:', this.ciasAereasFormArray.length);

      console.log('addCiaAerea: Concluído com sucesso');


    } catch (error) {
      console.error('addCiaAerea: Erro capturado:', error);
      throw error;
    }
  }

  private createCiaAereaFormGroupWithData(data?: any): FormGroup {
    const formGroup = this._formBuilder.group({
      ciaAerea: [data?.ciaAerea || null, [Validators.required]],
      nacInt: [data?.nacInt || null, [Validators.required]],
      percentualRav: [data?.percentualRav || 0, [Validators.required]],
      valorMinimoRav: [data?.valorMinimoRav || 0, [Validators.required]],
      moeda: [data?.moeda || null, [Validators.required]],
      variavel: [data?.variavel || false], 
      exceptions: [data?.exceptions || []],
      exceptionsControl: [null],
      blockId: [data?.blockId || ''] 
    });

    if (!data) {
      formGroup.markAsPristine();
      formGroup.markAsUntouched();
    }

    return formGroup;
  }

  public removeCiaAerea(index: number): void {
    this.ciasAereasFormArray.removeAt(index);
  }

  public openBulkCreateModal(): void {
    console.log('Abrindo modal de cadastro em massa');
    this.showBulkModal = true;
    this.selectedCias = [];

    this.bulkFormGroup.patchValue({
      selectedCia: null,
      nacInt: null,
      moeda: null,
      percentualRav: 0,
      valorMinimoRav: 0,
      variavel: false
    });

    console.log('Formulário resetado:', this.bulkFormGroup.value);

    this.bulkCiasEndpointKey = null;
    console.log('Modal aberto, aguardando seleção de status');

    this.bulkFormGroup.get('percentualRav')?.valueChanges.subscribe(value => {
      console.log('percentualRav mudou para:', value);
    });

    this.bulkFormGroup.get('valorMinimoRav')?.valueChanges.subscribe(value => {
      console.log('valorMinimoRav mudou para:', value);
    });
  }

  public closeBulkModal(): void {
    console.log('=== closeBulkModal CHAMADO ===');
    console.log('Stack trace:', new Error().stack);

    this.showBulkModal = false;
    this.selectedCias = [];
    this.editingBlockId = null; // Resetar o estado de edição
    this.bulkFormGroup.reset();

    console.log('Modal fechado, showBulkModal:', this.showBulkModal);
  }

  public async onBulkCiaSelection(selectedCia: any): Promise<void> {
    console.log('Cia selecionada no bulk:', selectedCia);
    if (selectedCia) {
      if (selectedCia.id === 0) {
        console.log('Não é possível selecionar "TODAS AS CIAS" no cadastro em massa');
        this.snackBar.open('Não é possível selecionar "TODAS AS CIAS" no cadastro em massa. Selecione cias específicas.', 'Fechar', {
          duration: 4000
        });
        this.bulkFormGroup.get('selectedCia')?.setValue(null);
        return;
      }

      const selectedStatus = this.bulkFormGroup.get('nacInt')?.value;
      if (!selectedStatus) {
        this.snackBar.open('Selecione primeiro o status (Nacional/Internacional/Ambos) antes de escolher as cias aéreas.', 'Fechar', {
          duration: 4000
        });
        this.bulkFormGroup.get('selectedCia')?.setValue(null);
        return;
      }

      if (this.data?.id) {
        try {
          const statusDisponiveis = await this.getStatusDisponiveis(selectedCia.codigo);
          if (!statusDisponiveis.includes(selectedStatus)) {
            this.snackBar.open(`A cia ${selectedCia.nome} não pode ser configurada com status ${selectedStatus}. Status disponíveis: ${statusDisponiveis.join(', ')}.`, 'Fechar', {
              duration: 5000
            });
            this.bulkFormGroup.get('selectedCia')?.setValue(null);
            return;
          }
        } catch (error) {
          console.error('Erro ao verificar status disponíveis:', error);
        }
      }

      if (!this.selectedCias.find(cia => cia.id === selectedCia.id)) {
        this.selectedCias.push(selectedCia);
        console.log('Cia adicionada:', selectedCia.nome, 'Total:', this.selectedCias.length);
      } else {
        console.log('Cia já foi selecionada:', selectedCia.nome);
        this.snackBar.open('Esta cia aérea já foi selecionada.', 'Fechar', {
          duration: 3000
        });
      }

      this.bulkFormGroup.get('selectedCia')?.setValue(null);
    }
  }

  public removeBulkCia(ciaToRemove: any): void {
    this.selectedCias = this.selectedCias.filter(cia => cia.id !== ciaToRemove.id);
  }

  public onBulkStatusChange(selectedValue?: any): void {
    const hadSelectedCias = this.selectedCias.length > 0;

    let statusId = null;
    if (selectedValue) {
      statusId = typeof selectedValue === 'object' ? selectedValue.id : selectedValue;
    } else {
      const formValue = this.bulkFormGroup.get('nacInt')?.value;
      statusId = typeof formValue === 'object' ? formValue.id : formValue;
    }

    console.log('=== onBulkStatusChange ===');
    console.log('selectedValue passado:', selectedValue);
    console.log('statusId extraído:', statusId);
    console.log('Tipo do statusId:', typeof statusId);

    if (!statusId) {
      console.log('StatusId é null/undefined, ignorando mudança de inicialização');
      return;
    }

    console.log('Processando mudança de status válida');
    console.log('Limpando cias selecionadas');
    this.selectedCias = [];

    this.bulkFormGroup.get('selectedCia')?.setValue(null);

    const newEndpoint = this.getBulkCiasEndpointWithStatus(statusId);
    console.log('Novo endpoint calculado:', newEndpoint);

    if (newEndpoint) {
      this.bulkCiasEndpointKey = newEndpoint + '?t=' + Date.now(); 
      console.log('Endpoint com timestamp:', this.bulkCiasEndpointKey);
    } else {
      this.bulkCiasEndpointKey = null;
      console.log('Endpoint limpo (null)');
    }

    if (hadSelectedCias) {
      this.snackBar.open('Status alterado. Selecione novamente as cias aéreas.', 'Fechar', {
        duration: 3000
      });
    }
  }

  public createBulkRecords(): void {
    console.log('=== INÍCIO createBulkRecords ===');
    console.log('Formulário válido?', this.bulkFormGroup.valid);
    console.log('Cias selecionadas:', this.selectedCias.length);
    console.log('Editando bloco existente?', this.editingBlockId);
    console.log('Valores do formulário ANTES:', this.bulkFormGroup.value);

    if (!this.bulkFormGroup.valid || this.selectedCias.length === 0) {
      console.log('Formulário inválido ou nenhuma cia selecionada');
      this.snackBar.open('Preencha todos os campos obrigatórios e selecione pelo menos uma cia aérea.', 'Fechar', {
        duration: 3000
      });
      return;
    }

    const formValue = this.bulkFormGroup.value;

    if (this.editingBlockId) {
      this.updateBulkRecords(this.editingBlockId, formValue);
    } else {
      this.createNewBulkRecords(formValue);
    }
  }

  private createNewBulkRecords(formValue: any): void {
    const blockId = this.generateBlockId(); 

    console.log('Criando registros em massa:', {
      cias: this.selectedCias,
      config: formValue,
      blockId: blockId
    });

    this.selectedCias.forEach((cia, index) => {
      console.log(`=== Criando registro ${index + 1} para cia:`, cia);
      console.log('Valores que serão usados:', {
        ciaAerea: cia,
        nacInt: formValue.nacInt,
        percentualRav: formValue.percentualRav,
        valorMinimoRav: formValue.valorMinimoRav,
        moeda: formValue.moeda,
        variavel: formValue.variavel,
        blockId: blockId
      });

      const newFormGroup = this._formBuilder.group({
        ciaAerea: [cia],
        nacInt: [formValue.nacInt],
        percentualRav: [formValue.percentualRav || 0],
        valorMinimoRav: [formValue.valorMinimoRav || 0],
        moeda: [formValue.moeda],
        variavel: [formValue.variavel || false],
        exceptions: [[]],
        exceptionsControl: [null],
        blockId: [blockId] // Adicionar o ID do bloco
      });

      console.log('FormGroup criado com valores:', newFormGroup.value);
      this.ciasAereasFormArray.push(newFormGroup);
    });

    console.log('Total de registros após criação em massa:', this.ciasAereasFormArray.length);
    console.log('=== FIM createNewBulkRecords ===');
    this.closeBulkModal();

    setTimeout(() => this.updateFieldsDisabledState(), 100);
  }

  private updateBulkRecords(blockId: string, formValue: any): void {
    console.log('Atualizando registros do bloco:', blockId);

    const indicesToUpdate: number[] = [];
    for (let i = 0; i < this.ciasAereasFormArray.length; i++) {
      const formGroup = this.ciasAereasFormArray.at(i) as FormGroup;
      const currentBlockId = formGroup.get('blockId')?.value;
      if (currentBlockId === blockId) {
        indicesToUpdate.push(i);
      }
    }

    console.log('Índices a serem atualizados:', indicesToUpdate);

    indicesToUpdate.reverse().forEach(index => {
      this.ciasAereasFormArray.removeAt(index);
    });

    this.selectedCias.forEach((cia, index) => {
      console.log(`=== Atualizando registro ${index + 1} para cia:`, cia);

      const newFormGroup = this._formBuilder.group({
        ciaAerea: [cia],
        nacInt: [formValue.nacInt],
        percentualRav: [formValue.percentualRav || 0],
        valorMinimoRav: [formValue.valorMinimoRav || 0],
        moeda: [formValue.moeda],
        variavel: [formValue.variavel || false],
        exceptions: [[]],
        exceptionsControl: [null],
        blockId: [blockId] // Manter o mesmo blockId
      });

      console.log('FormGroup atualizado com valores:', newFormGroup.value);
      this.ciasAereasFormArray.push(newFormGroup);
    });

    console.log('Total de registros após atualização em massa:', this.ciasAereasFormArray.length);
    console.log('=== FIM updateBulkRecords ===');
    this.closeBulkModal();

    setTimeout(() => this.updateFieldsDisabledState(), 100);

    this.snackBar.open(`${this.selectedCias.length} configurações atualizadas com sucesso!`, 'Fechar', {
      duration: 3000
    });
  }

  private generateBlockId(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  public isBlockRecord(index: number): boolean {
    const formGroup = this.ciasAereasFormArray.at(index) as FormGroup;
    const blockId = formGroup.get('blockId')?.value;
    return blockId && blockId.trim() !== '';
  }

  public hasBlockId(form: AbstractControl): boolean {
    const formGroup = form as FormGroup;
    const blockId = formGroup.get('blockId')?.value;

    const result = !!(blockId && blockId.trim() !== '');

    // Debug limitado para evitar spam
    const ciaAerea = formGroup.get('ciaAerea')?.value;
    if (Math.random() < 0.01) { // Log apenas 1% das chamadas
      // console.log('hasBlockId debug:', {
      //   blockId: blockId,
      //   result: result,
      //   ciaAerea: ciaAerea?.nome || ciaAerea?.codigo || 'N/A',
      //   ciaId: ciaAerea?.id || 'N/A'
      // });
    }

    return result;
  }


  private updateFieldsDisabledState(): void {
    console.log('=== updateFieldsDisabledState INICIADO ===');
    this.ciasAereasFormArray.controls.forEach((control, index) => {
      const formGroup = control as FormGroup;
      const blockId = formGroup.get('blockId')?.value;
      const temBlockId = !!(blockId && blockId.trim() !== '');
      const ciaAerea = formGroup.get('ciaAerea')?.value;

      console.log(`Registro ${index}:`, {
        cia: ciaAerea?.nome || 'N/A',
        blockId: blockId,
        temBlockId: temBlockId,
        acao: temBlockId ? 'DESABILITAR' : 'HABILITAR'
      });

      const fieldsToToggle = ['percentualRav', 'valorMinimoRav'];

      fieldsToToggle.forEach(fieldName => {
        const field = formGroup.get(fieldName);
        if (field) {
          const estadoAntes = field.disabled ? 'DISABLED' : 'ENABLED';

          if (temBlockId) {
            // TEM blockId = DESABILITAR
            if (field.enabled) {
              field.disable();
            }
          } else {
            // NÃO TEM blockId = HABILITAR
            if (field.disabled) {
              field.enable();
            }
          }

        }
      });
    });
  }



  public openBulkEditModal(index: number): void {
    try {
      console.log('=== openBulkEditModal INICIADO ===', { index });

      const formGroup = this.ciasAereasFormArray.at(index) as FormGroup;
      const blockId = formGroup.get('blockId')?.value;

      if (!blockId) {
        console.error('Registro não possui blockId');
        return;
      }

      console.log('Abrindo modal de edição em massa para blockId:', blockId);

      this.loadBlockConfigurations(blockId);

      console.log('=== openBulkEditModal FINALIZADO ===');
    } catch (error) {
      console.error('ERRO em openBulkEditModal:', error);
    }
  }


  private loadBlockConfigurations(blockId: string): void {
    try {
      console.log('=== loadBlockConfigurations INICIADO ===', { blockId });

      this.restangular.one('configuracoesrav/rf/block', blockId).get().subscribe({
        next: (response: any) => {
          try {
            console.log('=== Response recebido ===', response);

            let configurations: any[] = [];
            if (response && response.data && Array.isArray(response.data)) {
              configurations = response.data;
            } else if (Array.isArray(response)) {
              configurations = response;
            } else {
              console.error('Estrutura de response inesperada:', response);
            }

            console.log('Configurações extraídas:', configurations);
            console.log('Tipo de configurations:', typeof configurations);
            console.log('É array?', Array.isArray(configurations));
            console.log('Tamanho:', configurations?.length);

            this.prepareBlockEditModal(configurations, blockId);
            console.log('=== loadBlockConfigurations SUCCESS ===');
          } catch (error) {
            console.error('ERRO no processamento da response:', error);
          }
        },
        error: (error) => {
          console.error('ERRO na requisição:', error);
          this.snackBar.open('Erro ao carregar configurações do bloco.', 'Fechar', {
            duration: 3000
          });
        }
      });
    } catch (error) {
      console.error('ERRO em loadBlockConfigurations:', error);
    }
  }


  private prepareBlockEditModal(configurations: any[], blockId: string): void {
    try {
      console.log('=== prepareBlockEditModal INICIADO ===', { blockId, configurations });

      if (!configurations || configurations.length === 0) {
        console.log('Nenhuma configuração encontrada');
        this.snackBar.open('Nenhuma configuração encontrada para este bloco.', 'Fechar', {
          duration: 3000
        });
        return;
      }

    let allCias: any[] = [];

    if (Array.isArray(configurations)) {
      if (configurations.length > 0 && configurations[0].ciasAereas) {
        configurations.forEach(config => {
          if (config.ciasAereas && Array.isArray(config.ciasAereas)) {
            allCias.push(...config.ciasAereas);
          }
        });
      } else {
        allCias = configurations;
      }
    } else {
      if (configurations && (configurations as any).ciasAereas) {
        allCias = (configurations as any).ciasAereas;
      } else {
        allCias = [];
      }
    }

    if (!Array.isArray(allCias)) {
      console.error('allCias não é um array:', allCias);
      this.snackBar.open('Erro na estrutura de dados retornada.', 'Fechar', {
        duration: 3000
      });
      return;
    }

    if (allCias.length === 0) {
      this.snackBar.open('Nenhuma cia aérea encontrada para este bloco.', 'Fechar', {
        duration: 3000
      });
      return;
    }

    const firstCia = allCias[0];

    const blockCias: any[] = allCias
      .filter(cia => cia.blockId === blockId)
      .map(cia => ({
        id: cia.id,
        codigo: cia.codigo,
        nome: cia.nome
      }));

    console.log('Cias do bloco filtradas:', blockCias);

    this.bulkFormGroup.patchValue({
      selectedCia: null,
      nacInt: firstCia.nacInt,
      moeda: firstCia.moeda,
      percentualRav: firstCia.percentualRav,
      valorMinimoRav: firstCia.valorMinimoRav,
      variavel: firstCia.variavel
    });

    // Definir as cias selecionadas
    this.selectedCias = blockCias;

    // Armazenar o blockId para edição
    this.editingBlockId = blockId;

    // Abrir o modal
    this.showBulkModal = true;
    console.log('showBulkModal definido como:', this.showBulkModal);

    console.log('Modal de edição em massa preparado:', {
      blockId: blockId,
      selectedCias: this.selectedCias,
      formValues: this.bulkFormGroup.value
    });

    console.log('=== prepareBlockEditModal FINALIZADO ===');
    } catch (error) {
      console.error('ERRO em prepareBlockEditModal:', error);
    }
  }

  public async ngOnInit() {
    window.addEventListener('error', (event) => {
      console.error('Erro global capturado:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('Promise rejeitada não tratada:', event.reason);
    });


    this.ravFormGroup = this._formBuilder.group({
      nome: [this.data ? this.data.nome : null],
      prioridade: [this.data ? this.data.prioridade : null],
      tipoTarifaAcordo: [this.data ? this.data.tipoTarifaAcordo : null],
      tipoConfigRav: [this.data ? this.data.tipoConfigRav : null],
      nacInt: [this.data ? this.data.nacInt : null],
      produtos: [this.data ? this.data.produtos : null],
      grupos: [this.data ? this.data.grupos : null],
      empresas: [this.data ? this.data.empresas : null],
      ativo: [this.data ? this.data.ativo : null],
      ciasAereas: this._formBuilder.array([]),
      statusPublicacao: [this.data ? this.data.statusPublicacao : null],
    });

    // Inicializar formulário de cadastro em massa
    this.bulkFormGroup = this._formBuilder.group({
      selectedCia: this._formBuilder.control(null),
      nacInt: this._formBuilder.control(null, Validators.required),
      moeda: this._formBuilder.control(null, Validators.required),
      percentualRav: this._formBuilder.control(0, Validators.required),
      valorMinimoRav: this._formBuilder.control(0, Validators.required),
      variavel: this._formBuilder.control(false)
    });

    this.bulkCiasEndpointKey = this.getBulkCiasEndpoint();
    const id = this.route.snapshot.params['id'];

    this.loading = false;
    this.initialized = true;

    if (!isNaN(id)) {
      this.loading = true;
      try {
        await this.loadItem(id);
      } finally {
        this.loading = false;
      }
    }
    this.convertPercentualRav();

    // Carregar cias disponíveis
    this.loadCiasDisponiveis();
  }

  private async loadCiasDisponiveis(): Promise<void> {
    try {
      this.ciasDisponiveis = await this.getCiasDisponiveis();
      console.log('Cias disponíveis carregadas:', this.ciasDisponiveis.length);
    } catch (error) {
      console.error('Erro ao carregar cias disponíveis:', error);
    }
  }

  private convertPercentualRav(){
    const ciasAereasArray = this.ravFormGroup.get('ciasAereas') as FormArray;
    const ciasAereasValues = ciasAereasArray.value.map(ciaAerea => ({
      ...ciaAerea,
      percentualRav: parseFloat((ciaAerea.percentualRav * 100).toFixed(2))
    }));
    ciasAereasArray.patchValue(ciasAereasValues);
  }

  protected async loadItem(id: number): Promise<void> {
    const response = await this.findById(id);
    await this.setValueItem(response);
  }

  protected async loadClone(id: any): Promise<void> {
    const response = await this.findCloneById(id);
    await this.setValueItem(response);
  }

  protected async setValueItem(data: any): Promise<void> {
    const {
      nome,
      empresas,
      produtos,
      grupos,
      ciasAereas,
      tipoTarifaAcordo,
      tipoConfigRav,
      nacInt,
      prioridade,
      ativo,
      statusPublicacao,
    } = data.body;

    this.data = data.body;

    let ciaAerea = orderBy(ciasAereas, 'nome', 'asc').map(
      cia => {
        
        let exceptions = [];
        if (cia.id === 0 && cia.exceptions && cia.exceptions.trim() !== '') {
          exceptions = cia.exceptions.split(',').map((nome: string) => ({
            nome: nome.trim(),
            id: null, 
            codigo: null
          })).filter((exc: any) => exc.nome !== '');
        }

        return {
          nacInt: cia.nacInt,
          percentualRav: cia.percentualRav,
          valorMinimoRav: cia.valorMinimoRav,
          moeda: cia.moeda,
          variavel: cia.variavel,
          ciaAerea: {codigo: cia.codigo, id: cia.id, nome: cia.nome},
          exceptions: exceptions,
          exceptionsControl: null, 
          blockId: cia.blockId || ''  
        }
      }
    );

    while (this.ciasAereasFormArray.length > 0) {
      this.ciasAereasFormArray.removeAt(0);
    }

    ciaAerea.forEach(ciaData => {
      this.ciasAereasFormArray.push(this.createCiaAereaFormGroupWithData(ciaData));
    });

    await this.populateStatusOptionsForExistingData();

    setTimeout(() => this.updateFieldsDisabledState(), 100);

    this.currentId = data.body.id;
    this.active = ativo;
    this.title = nome;
    this.ravFormGroup.setValue({
      nome,
      empresas,
      produtos,
      ciasAereas: ciaAerea,
      tipoTarifaAcordo,
      tipoConfigRav,
      nacInt,
      grupos,
      prioridade,
      ativo,
      statusPublicacao,
    });
  }

  private findById(id: number): Promise<any> {
    return this.restangular.one(this.RESOURCE_NAME, id).get().toPromise();
  }


  public async onSubmit(): Promise<void> {
    this.loading = true;
     if (this.ravFormGroup.controls.ativo.value == false ){
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Configuração Rav Inativa,  Restaure antes para poder ser editada',
          buttons: [{label: "Fechar", action: "close", color: "warn"}]
        }
      });
    } else {
      try {
        if (this.ravFormGroup.valid) {

          if (this.validate()) {
            let request: any;
            let values = this.formValue2Value({
              ...this.ravFormGroup.getRawValue(),
            });

            if (this.currentId == null) {
              values.ativo = true;
            }

            if (!isNaN(this.currentId)) {
              request = this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT(values);
            }
             else {
              request = this.restangular.all(this.RESOURCE_NAME).post(values);
            }

            try {
              await request.toPromise()
              this._router.navigateByUrl(this.RAVS)
            } catch (err) {
              this._snackBar.openFromComponent(SnackBarComponent, {
                data: {
                  httpErrorResponse: err,
                  buttons: [
                    {label: "Mais informações", action: "information", color: "warn"},
                    {label: "Fechar", action: "close", color: "warn"}],
                }
              });
            }
          }} else {
          this.ravFormGroup.markAllAsTouched();
          this._snackBar.openFromComponent(SnackBarComponent, {
            data: {
              title: 'Alerta',
              subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
              buttons: [{label: "Fechar", action: "close", color: "warn"}]
            }
          });
        }
      } finally {
        this.loading = false;
      }
    }
  }


  public showSaveAndPublishDialog(event: Event) {
    event.preventDefault();
    this.loadingSearch = true;
    if (this.ravFormGroup.controls.ativo.value == false ){
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Configuracao Rav Inativa,  Restaure antes para poder ser editada',
          buttons: [{label: "Fechar", action: "close", color: "warn"}]
        }
      });
      this.loadingSearch = false;
    } else {
      this.showConfirmationDialog(
        {
          title: 'Salvar e Publicar Configuração',
          message: 'Tem certeza de que deseja salvar e publicar este item?',
          buttons: [
            { label: 'Cancelar', action: 'close', color: 'cancel' },
            {
              label: 'Sim',
              action: (confirm) => {
                this.saveAndPublish();
                confirm();
              },
              color: 'save',
            },
          ],
          align: 'end',
        },
        () => this.saveAndPublish()
      );
    }
  }
  public saveAndPublish() {
    this.loading = true;
    try {
      if (this.ravFormGroup.valid) {
        let request: any;
        let value = this.formValue2Value({
          ...this.ravFormGroup.getRawValue(),
        });
        value.statusPublicacao = "PUBLICADA"; 
        value.ativo = true;
        if (this.currentId != null) {
          request = this.restangular
            .one(this.RESOURCE_NAME, this.currentId)
            .customPUT(value);
        } else {
          request = this.restangular.all(this.RESOURCE_NAME).post(value);
        }
        request.subscribe(
          async (response) => {
            const { id } = response.body;
            await this.restangular
              .one(this.RESOURCE_NAME, id)
              .customPUT({}, 'publicar')
              .subscribe((response) => {
                this._router.navigateByUrl('/ravs');
              });
          },
          (err) => {
            this.snackBar.openFromComponent(SnackBarComponent, {
              data: {
                httpErrorResponse: err,
                buttons: [
                  {
                    label: 'Mais informações',
                    action: 'information',
                    color: 'warn',
                  },
                  { label: 'Fechar', action: 'close', color: 'warn' },
                ],
              },
            });
            this.loadingSearch = false;
            this.loading = false;
          }
        );
      } else {
        this.loadingSearch = false;
        this.ravFormGroup.markAllAsTouched();
        this.snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle:
              'Verifique se todos os campos estão preenchidos corretamente',
            buttons: [{ label: 'Fechar', action: 'close', color: 'warn' }],
          },
        });
      }
    } finally {
      this.loading = false;
    }
  }

  public validate() {
    return (form: FormGroup) => {
      const values = form.value
      if (!(values.ravFormGroup)) {
        this._snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Defina ao menos uma Unidade Operacional',
            buttons: [{ label: "Fechar", action: "close", color: "warn" }]
          }
        });
        return false
      }
      return true
    }
  }

  protected async findCloneById(cloneId): Promise<void> {
    return this.restangular
      .one(this.RESOURCE_NAME, cloneId)
      .customGET('clonar').toPromise();
  }

  public async clone() {
    this.loading = true;
    try {
      this._router.navigate(['ravs' + '/' + 'criar'], {queryParams: {cloneId: this.currentId}});
      this.findCloneById(this.currentId).then(response => {
        this.currentId = null;
        this.setValueItem(response);
        this.convertPercentualRav();
      })
    } finally {
      this.loading = false;
    }
  }

  public restoreItemDialog(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: 'Alerta',
        message: "Deseja restaurar o registro? ao ser restaurado o status da configuração será NÃO PUBLICADO",
        buttons: [
          { label: 'Não', action: 'close', color: 'cancel' },
          {
            label: 'Sim',
            action: (confirm) => {
              this.restoreItem();
              confirm();
            },
            color: 'save',
          },
        ],
        align: 'end',
      },
      () => this.restoreItem()
    );
  }

  public async restoreItem() {
    this.loading = true;
    try {
      await this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT({}, 'ativar').toPromise()
      await this.loadItem(this.currentId);
    } catch (err) {
      this._snackBar.openFromComponent(SnackBarComponent, {
        data: {
          httpErrorResponse: err,
          buttons: [
            {label: "Mais informações", action: "information", color: "warn"},
            {label: "Fechar", action: "close", color: "warn"}]
        }
      });
    } finally {
      this.loading = false;
    }
  }

  public removeItem(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: "Alerta",
        message: "Deseja inativar o registro?",
        buttons: [{label: "Não", action: "close", color: "cancel"},
          {
            label: "Sim", action: (confirm) => {
              this.deleteItem()
              confirm()
            }, color: "save"
          }],
        align: "end"
      }, () => this.deleteItem())
  }

  private async deleteItem() {
    this.loading = true;
    try {
      await this.restangular.one(this.RESOURCE_NAME, this.currentId).remove().toPromise()
      if (this.data.statusPublicacao === 'NAO_PUBLICADA') {
        await this._router.navigateByUrl(this.RAVS);
      } else {
        await this.loadItem(this.currentId);
      }
    } finally {
      this.loading = false;
    }
  }

  private showConfirmationDialog(data: DialogData, action: Function): void {
    const dialogRef = this.dialog.open(DialogComponent, {
      maxWidth: "400px",
      data: data
    });
  }

  public async feedHist(hist, callback) {
    if (hist.configuracaoRav.diff === undefined) {
      await this.restangular.one(this.RESOURCE_NAME, hist.configuracaoRav.id).get()
        .subscribe(response => {
          let body = response.body;
          let historico = camposComparaveisConfiguracao(body);
          let atual = camposComparaveisConfiguracao(this.data);

          if(!historico.editavel && !historico.ativo && !atual.ativo){
            historico.ativo = true;
          }
          let diff = this.objectDiff.diff(historico, atual);
          hist.configuracaoRav.diff = diff;
          callback(this.objectDiff.toJsonDiffView(diff));
        });
    }

    function camposComparaveisConfiguracao(data) {
      return {
        nome: data.nome,
        ativo: data.ativo,
        editavel: data.editavel,
        prioridade: data.prioridade,
        tipoConfigRav: data.tipoConfigRav,
        nacInt: data.nacInt,
        tipoTarifaAcordo: data.tipoTarifaAcordo,
        produtos: camposComparaveisProdutos(data.produtos),
        empresas: camposComparaveisEmpresas(data.empresas),
        ciasAereas: camposComparaveisCiasAereas(data.ciasAereas),
        statusPublicacao: data.statusPublicacao,
      }
    }

    function camposComparaveisCiasAereas(ciasAereas) {
      return ciasAereas.map(ciaAerea => ({
        nome: ciaAerea.nome,
        nacInt: ciaAerea.nacInt,
        moeda: ciaAerea.moeda,
        percentualRav: ciaAerea.percentualRav,
        valorMinimoRav: ciaAerea.valorMinimoRav,
        variavel: ciaAerea.variavel,
      }))
    }

    function camposComparaveisEmpresas(empresas) {
      return empresas.map(empresa => ({
        id: empresa.id,
        nome: empresa.nome,
        referencia: empresa.referencia,
        codigoPais: empresa.codigoPais,
      }))
    }

    function camposComparaveisProdutos(produtos) {
      return produtos.map(produto => ({
        id: produto.id,
        nome: produto.nome,
      }))
    }
  }

  public showHistory(event: Event): void {
    event.preventDefault();
    this.data.historico.forEach((element, index) => {
      if (index === this.data.historico.length - 1) {
        this.feedHist(element, (diff) => {
          element.diff = diff;
          this.dialog.open(SimpleHistoryComponent, {
            autoFocus: false,
            maxHeight: '350px',
            width: '700px',
            data: this.data.historico,
          });
        });
      } else {
        this.feedHist(element, (diff) => {
          element.diff = diff;
        });
      }
    });
  }

}
