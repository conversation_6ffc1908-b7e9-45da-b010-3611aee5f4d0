import { Injectable } from "@angular/core";

@Injectable({
  providedIn: "root",
})
export class PagesService {
  constructor() {}

  public getHrefById(idMenu: string) {
    return this.getPages().find((i) => i.id === idMenu)?.href;
  }

  public getPages(): {
    id: string;
    name: string;
    icon: string;
    state: string;
    group: string;
    href: string;
    isNew?: true;
  }[] {
    return [
      {
        id: "buscaDisponibilidade",
        name: "Busca de Disponibilidade",
        icon: "airplane",
        state: "",
        group: "Disponibilidade",
        href: "availabilitySearch",
        isNew: true,
      },
      {
        id: "configuracoesBusca",
        name: "Configurações de busca",
        icon: "search",
        state: "app.searchSettings",
        group: "Disponibilidade",
        href: "searchSettings",
        isNew: true,
      },
      {
        id: "configuracoesPrecificacao",
        name: "Precificações",
        icon: "money",
        state: "",
        group: "Precificacao",
        href: "pricingSettings",
        isNew: true,
      },
      {
        id: "configuracoesPrecificacaoComercial",
        name: "Precificações Comerciais",
        icon: "money",
        state: "app.commercialPricingSettings",
        group: "Precificacao",
        href: "commercialPricingSettings",
        isNew: true,
      },
      {
        id: "",
        name: "Precificação integração",
        icon: "chart",
        state: "",
        group: "Precificacao",
        href: "pricingIntegration",
        isNew: true,
      },
      {
        id: "configuracaofee",
        name: "Fee's",
        icon: "money-box",
        state: "app.feeSettings",
        group: "Precificacao",
        href: "configuracaofee",
        isNew: true,
      },
      {
        id: "configuracoesrav",
        name: "Rav's",
        icon: "money-box",
        state: "",
        group: "Precificacao",
        href: "ravs",
        isNew: true,
      },
      {
        id: "configuracoesdu",
        name: "Du's",
        icon: "money-box",
        state: "",
        group: "Precificacao",
        href: "duSettings",
        isNew: true,
      },
      {
        id: "taxaEmbarque",
        name: "Taxa de Embarque",
        icon: "money-box",
        state: "",
        group: "Precificacao",
        href: "boardingFee",
        isNew: true,
      },
      {
        id: "condicaoPagamento",
        name: "Condição de Pagamento",
        icon: "card",
        state: "paymentTerms",
        group: "Precificacao",
        href: "condicoes-de-pagamento",
        isNew: true,
      },
      {
        id: "acordosComerciais",
        name: "Acordos Comerciais",
        icon: "card-travel",
        state: "",
        group: "Disponibilidade",
        href: "tradeAgreements",
        isNew: true,
      },
      {
        id: "perfilTarifario",
        name: "Perfis Tarifarios",
        icon: "local-mall",
        state: "",
        group: "Disponibilidade",
        href: "rateProfiles",
        isNew: true,
      },
      {
        id: "perfilPorPrograma",
        name: "Perfis Tarifarios - Por Programa",
        icon: "local-mall",
        state: "",
        group: "Disponibilidade",
        href: "programFareProfile",
        isNew: true,
      },
      {
        id: "importaLoc",
        name: "Importação de Loc - Acordo Padrão",
        icon: "file-plus",
        state: "",
        group: "Reservas",
        href: "reservationImport",
        isNew: true,
      },
      // Ocultado devido a confirmacao do nao uso do menu
      // {
      //   id: 'features',
      //   name: 'Funcionalidades e Notificações',
      //   icon: 'notifications-active',
      //   state: 'app.feature',
      //   group: 'Configurações',
      //   href: 'feature',
      // },
      {
        id: "bloqueioSistemaEmissor",
        name: "Bloqueio de Sistema Emissor",
        icon: "block",
        state: "",
        group: "Configurações",
        href: "emissionSystemBlock",
        isNew: true,
      },
      // Ocultado devido a confirmacao do nao uso do menu de Dashboard pelo time de Conectividade
      // {
      //   id: 'dashboard',
      //   name: 'Dashboard',
      //   icon: 'view-dashboard',
      //   state: 'app.dashboard',
      //   group: 'Reservas',
      //   href: 'dashboard',
      // },
      /*       {
              id: 'configuracaoScore',
              name: 'Configuração de Score Captura',
              icon: 'chart',
              state: 'app.scoreSettings',
              group: 'Guardião',
              href: 'configuracaoScore',
            }, */
      {
        id: "credencialCaptura",
        name: "Credencial de Captura de Reservas",
        icon: "chart",
        state: "",
        group: "Guardião",
        href: "credentialCapture",
        isNew: true,
      },
      {
        id: "credencialScheduleChange",
        name: "Credencial de Schedule Change",
        icon: "chart",
        state: "",
        group: "Guardião",
        href: "credencialScheduleChange",
        isNew: true,
      },
      /*       {
        id: 'captures',
        name: 'Captura de Reservas',
        icon: 'chart',
        state: 'app.captures',
        group: 'Guardião',
        href: 'captures',
      }, */
      {
        id: "relatorioReservasEmissoes",
        name: "Depuração de Reservas",
        icon: "chart",
        state: "",
        group: "Reservas",
        href: "depuracaoReserva",
        isNew: true,
      },
      // {
      //   id: 'comparaPreco',
      //   name: 'Comparativo de Preço',
      //   icon: 'chart',
      //   state: '',
      //   group: 'Comparativo de Preços',
      //   href: 'comparePrice',
      //   isNew: true,
      // },
      // {
      //   id: 'solicitacaoComparativo',
      //   name: 'Solicitações Comparativo de Preço',
      //   icon: 'file-text',
      //   state: '',
      //   group: 'Comparativo de Preços',
      //   href: 'comparePriceSolicitations',
      //   isNew: true,
      // },
      // Ocultado devido ao não uso do menu
      // {
      //   id: 'otato',
      //   name: 'Otato',
      //   icon: 'label',
      //   state: 'app.otato',
      //   group: 'Cadastros',
      //   href: 'otato',
      // },
      // {
      //   id: 'voosExcluidos',
      //   name: 'Vôos excluídos',
      //   icon: 'airplane-off',
      //   state: 'app.excludedFlights',
      // },
      {
        id: "credenciais",
        name: "Credenciais",
        icon: "badge-check",
        state: "",
        group: "Cadastros",
        href: "credenciais",
        isNew: true,
      },
      {
        id: "passenger",
        name: "Passageiros White List",
        icon: "accounts",
        state: "",
        group: "Cadastros",
        href: "passenger",
        isNew: true,
      },
      {
        id: "credencialPaymentHub",
        name: "Credenciais de Payment Hub",
        icon: "balance-wallet",
        state: "",
        group: "Cadastros",
        href: "credencialPaymentHub",
        isNew: true,
      },
      {
        id: "contato",
        name: "Contatos",
        icon: "account-box-phone",
        state: "",
        group: "Cadastros",
        href: "contact",
        isNew: true,
      },
      {
        id: "codigocontrato",
        name: "Contratos",
        icon: "file-text",
        state: "",
        group: "Cadastros",
        href: "contract",
        isNew: true,
      },
      {
        id: "empresas",
        name: "Empresas",
        icon: "city-alt",
        state: "",
        group: "Empresa",
        href: "company",
        isNew: true,
      },
      {
        id: "configuracoesptcs",
        name: "PTC's",
        icon: "face",
        state: "",
        group: "Cadastros",
        href: "ptc",
        isNew: true,
      },
      {
        id: "ciasAereas",
        name: "Cias. Aéreas",
        icon: "airplane",
        state: "",
        group: "Cadastros",
        href: "aircompanies",
        isNew: true,
      },
      {
        id: "grupos",
        name: "Grupos",
        icon: "globe-alt",
        state: "",
        group: "Empresa",
        href: "group",
        isNew: true,
      },
      {
        id: "produtos",
        name: "Produtos",
        icon: "label-heart",
        state: "",
        group: "Cadastros",
        href: "product",
        isNew: true,
      },
      {
        id: "integrators",
        name: "Integrators",
        icon: "globe-alt",
        state: "",
        group: "Cadastros",
        href: "integrators",
        isNew: true,
      },
      {
        id: "usuarios",
        name: "Usuários Integradores",
        icon: "swap",
        state: "",
        group: "Cadastros",
        href: "usuariosIntegradores",
        isNew: true,
      },
      {
        id: "usuariosWeb",
        name: "Usuários Web",
        icon: "accounts",
        state: "",
        group: "Cadastros",
        href: "user-front",
        isNew: true,
      },
      {
        id: "",
        name: "Cadastro Continente",
        icon: "globe",
        state: "",
        group: "Cadastros",
        href: "registrationContinente",
        isNew: true,
      },
      {
        id: "",
        name: "Cadastro País",
        icon: "globe",
        state: "",
        group: "Cadastros",
        href: "registrationCountry",
        isNew: true,
      },
      {
        id: "",
        name: "Cadastro Estado",
        icon: "globe",
        state: "",
        group: "Cadastros",
        href: "registrationState",
        isNew: true,
      },
      {
        id: "senhaServidorEmail",
        name: "Senha servidor Email",
        icon: "accounts",
        state: "",
        group: "Cadastros",
        href: "senhaServidorEmail",
        isNew: true,
      },
      {
        id: "aeroportos",
        name: "Aeroportos",
        icon: "airplane",
        state: "",
        group: "Cadastros",
        href: "aeroportos",
        isNew: true,
      },
      {
        id: "perfilAcesso",
        name: "Perfis",
        icon: "globe-lock",
        state: "",
        group: "Cadastros",
        href: "perfis",
        isNew: true,
      },
      {
        id: "filiais",
        name: "Filiais",
        icon: "pin",
        state: "",
        group: "Empresa",
        href: "subsidiary",
        isNew: true,
      },
      {
        id: "agencias",
        name: "Agências",
        icon: "coffee",
        state: "",
        group: "Empresa",
        href: "agency",
        isNew: true,
      },
      {
        id: "agrupamentosCiasAereas",
        name: "Grupos de Cias. Aéreas",
        icon: "flag",
        state: "",
        group: "Cadastros",
        href: "grupos-de-cias-aereas",
        isNew: true,
      },

      {
        id: "unidadeoperacional",
        name: "Unidades Operacionais",
        icon: "store",
        state: "",
        group: "Empresa",
        href: "operationalUnit",
        isNew: true,
      },
      {
        id: "rotas",
        name: "Rotas",
        icon: "directions",
        state: "",
        group: "Configurações",
        href: "routes",
        isNew: true,
      },
      {
        id: "rotas",
        name: "Rotas V2",
        icon: "directions",
        state: "",
        group: "Configurações",
        href: "routesV2",
        isNew: true,
      },
      {
        id: "auditoria",
        name: "Auditoria",
        icon: "directions",
        state: "",
        group: "Configurações",
        href: "audit",
        isNew: true,
      },
      // Novos Menus
      {
        id: "markupEdit",
        name: "Alteração de Markup",
        icon: "settings-square",
        state: "",
        group: "Novas Ferramentas",
        href: "markupEdit",
        isNew: true,
      },
      {
        id: "repositories",
        name: "Repositório de contratos",
        icon: "folder",
        state: "",
        group: "Novas Ferramentas",
        href: "repositories",
        isNew: true,
      },
      {
        id: "distributor",
        name: "Distribuição de conteúdo",
        icon: "card-travel",
        state: "",
        group: "Novas Ferramentas",
        href: "distributor",
        isNew: true,
      },
      // {
      //   id: 'configuracoesPrecificacaoFretamento',
      //   name: 'Precificacao do Fretamento',
      //   icon: 'money',
      //   state: '',
      //   group: 'Precificacao',
      //   href: 'configuracoesPrecificacaoFretamento',
      //   isNew: true,
      // },
      {
        id: "empresaPermissao",
        name: "Empresa Permissao",
        icon: "money",
        state: "",
        group: "Empresa",
        href: "companyPermission",
        isNew: true,
      },
      {
        id: "locCancel",
        name: "Cancelamento de Localizadores",
        icon: "block",
        state: "",
        group: "Reservas",
        href: "locCancel",
        isNew: true,
      },
      {
        id: "sistemaEmissorInativoBuscaDisp",
        name: "Sistema Emissor Inativo Busca Disp.",
        icon: "account-box-phone",
        state: "",
        group: "Disponibilidade",
        href: "sistema-emissor-inativo-busca-disp",
        isNew: true,
      },
      {
        id: "flagfeature",
        name: "Flag Feature",
        icon: "flag",
        state: "",
        group: "Novas Ferramentas",
        href: "flag-feature",
        isNew: true,
      },
      {
        id: "airlineTariffChecker",
        name: "Airline Tariff Checker",
        icon: "chart",
        state: "",
        group: "Novas Ferramentas",
        href: "airlineTariffChecker",
        isNew: true,
      },
      {
        id: "emissionSystemVhiPlus",
        name: "Sistema Emissor VHI Plus",
        icon: "chart",
        state: "",
        group: "Configurações",
        href: "emissionSystemVhiPlus",
        isNew: true,
      },
      {
        id: "",
        name: "Filtros de Recomendações",
        icon: "search",
        state: "",
        group: "Disponibilidade",
        href: "filtroRecomendacoes",
        isNew: true,
      },
      {
        id: "configuracaoProduto",
        name: "Bloqueio venda de Aéreo sem Hotel",
        icon: "settings-square",
        state: "",
        group: "Cadastros",
        href: "configuracaoProduto",
        isNew: true,
      },
      {
        id: "localidades",
        name: "Localidades",
        icon: "directions",
        state: "",
        group: "Cadastros",
        href: "localidades",
        isNew: true,
      },
    ];
  }
}
