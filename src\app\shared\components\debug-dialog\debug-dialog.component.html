<div fxLayout="column" fxFlexFill>
    <h2>
      <span>DEPURAÇÃO PRECIFICÁVEL</span>
      <mat-divider></mat-divider>
    </h2>

    <mat-list-item>
      <div fxFlex fxLayoutGap="30px" fxLayout="column">



        <mat-chip-list>
          <mat-basic-chip class="dark-chip">Depuração: {{data.id}}</mat-basic-chip>
        </mat-chip-list>

        <div fxLayoutGap="10px" fxFlex fxFlexAlign="space-between center">
          <mat-chip-list>
            <mat-basic-chip *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.excluido" class="red-chip">Vôo Excluido</mat-basic-chip>
            <mat-basic-chip *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.excluido" class="dark-chip-click" (click)="copyRawJson()">Raw Json</mat-basic-chip>
            <mat-basic-chip *ngIf="!data.precificacaoTesteResponse.resposta.resultadoDebug.excluido" class="dark-chip-click" (click)="copyRawJson()">Raw Json</mat-basic-chip>
          </mat-chip-list>
        </div>

        <mat-chip-list *ngIf="data.precificacaoTesteResponse.precificavelHash">
          <mat-basic-chip class="dark-chip-click" (click)="copyHash(data.precificacaoTesteResponse.precificavelHash)">
            {{ data.precificacaoTesteResponse.precificavelHash | limit: [30]}}
          </mat-basic-chip>
        </mat-chip-list>

        <ng-container *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesPrecificacao.length > 0">
          <div *ngFor="let config of data.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesPrecificacao" fxLayoutGap="10px" fxFlex fxLayout="column">
            <div fxFlex fxLayout="row" fxLayoutAlign="start center">
              <mat-basic-chip class="dark-chip">P{{config.prioridade}}</mat-basic-chip>
              <a target="_blank" (click)="redirect($event, 'pricingSettings', config.id)">
                <span>{{ config.id }} - {{ config.nome }}</span>
              </a>
            </div>
          </div>
        </ng-container>

        <div fxFlex fxLayout="row">
          <span *ngIf="data.precificacaoTesteResponse.resposta?.idConfigSucessor">Config Sucessor</span>
          <mat-basic-chip class="dark-chip-click" (click)="redirect($event, 'pricingSettings', data.precificacaoTesteResponse.resposta?.idConfigSucessor)">
            {{data.precificacaoTesteResponse.resposta?.idConfigSucessor ? data.precificacaoTesteResponse.resposta.idConfigSucessor : 'S/Config Sucessor'}}
          </mat-basic-chip>
        </div>

        <div *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoFee?.id" fxLayoutGap="10px" fxFlex fxLayout="column">
          <div fxFlex fxLayout="row" fxLayoutAlign="start center">
            <mat-chip-list>
              <mat-basic-chip class="dark-chip" style="margin-right: 8px;">P{{data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoFee.prioridade}}</mat-basic-chip>
            </mat-chip-list>
            <a target="_blank" (click)="redirect($event, 'configuracaofee', data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoFee.id)">
              <span>{{ data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoFee.id }} - {{ data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoFee.nome }}</span>
            </a>
          </div>
        </div>

        <div *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoRav && data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoRav.id" fxLayoutGap="10px" fxFlex fxLayout="column">
          <div fxFlex fxLayout="row" fxLayoutAlign="start center">
            <mat-chip-list>
              <mat-basic-chip class="dark-chip" style="margin-right: 8px;">P{{data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoRav.prioridade}}</mat-basic-chip>
            </mat-chip-list>
            <a target="_blank" (click)="redirect($event, 'ravs', data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoRav.id)">
              <span>{{ data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoRav.id }} - {{ data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoRav.nome }}</span>
            </a>
          </div>
        </div>

        <div *ngIf="data.precificavelRE.configuracaoRavId" fxLayoutGap="10px" fxFlex fxLayout="row">
          Configuração RAV aplicada ID
          <div fxFlex fxLayout="row" fxLayoutAlign="start center" >
              <mat-chip-list style="margin-left: 8px;">
                <mat-basic-chip  class="dark-chip-click"
                  (click)="redirect($event, 'ravs', data.precificavelRE.configuracaoRavId)">
                  {{data.precificavelRE.configuracaoRavId}}
                </mat-basic-chip>
              </mat-chip-list>
          </div>
        </div>

        <div *ngIf="data.precificavelRE.configuracaoFeeId" fxLayoutGap="10px" fxFlex fxLayout="row">
          Configuração FEE aplicada ID
          <div fxFlex fxLayout="row" fxLayoutAlign="start center" >
              <mat-chip-list style="margin-left: 8px;">
                <mat-basic-chip  class="dark-chip-click"
                  (click)="redirect($event, 'configuracaofee', data.precificavelRE.configuracaoFeeId)">
                  {{data.precificavelRE.configuracaoFeeId}}
                </mat-basic-chip>
              </mat-chip-list>
          </div>
        </div>

        <div *ngFor="let config of data.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesPrecificacaoComercial" fxLayoutGap="10px" fxFlex fxLayout="column">
          <div fxFlex fxLayout="row" fxLayoutAlign="start center">
            <mat-chip-list>
              <mat-basic-chip class="dark-chip">{{config.prioridade}}</mat-basic-chip>
            </mat-chip-list>
            <span>{{ config.id }} - {{ config.nome }}</span>
          </div>
        </div>


        <div *ngFor="let segmento of data.precificavelRE.segmentos" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center">
          <mat-expansion-panel hideToggle fxFlex fxFlexAlign="center">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div fxLayoutGap="4px" fxFlex fxLayoutAlign="start center" fxLayout="row">
                  <small style="color: gray">{{segmento.dataOrigem | date:"dd/MM/yyyy"}}</small>
                  <mat-basic-chip class="dark-chip">{{segmento.aeroportoOrigem}}</mat-basic-chip>
                  <small>&#8594;</small>
                  <small style="color: gray">{{segmento.dataDestino | date:"dd/MM/yyyy"}}</small>
                  <mat-basic-chip class="dark-chip">{{segmento.aeroportoDestino}}</mat-basic-chip>
                </div>
              </mat-panel-title>
            </mat-expansion-panel-header>

            <ng-template matExpansionPanelContent>
              <div fxLayout="column">
                <div *ngFor="let voo of segmento.voos" fxFlex fxFlexAlign="space-between center">
                  <mat-basic-chip class="dark-chip">{{voo.numero}}</mat-basic-chip>
                  <mat-basic-chip *ngIf="voo.escalas.length > 0" class="dark-chip">{{voo.escalas | json}}</mat-basic-chip>
                  <mat-basic-chip class="dark-chip">Cia M: {{voo.ciaAereaMarketing}}</mat-basic-chip>
                  <mat-basic-chip class="dark-chip">Cia C: {{voo.ciaAereaCodeShare}}</mat-basic-chip>
                  <mat-basic-chip class="dark-chip">{{voo.regraTarifaAerea?.classe}}</mat-basic-chip>
                  <mat-basic-chip class="dark-chip">{{voo.regraTarifaAerea?.cabine}}</mat-basic-chip>
                  <mat-basic-chip class="dark-chip">{{voo.regraTarifaAerea?.baseTarifaria}}</mat-basic-chip>
                  <mat-basic-chip *ngIf="voo.regraTarifaAerea?.designadorBase" class="dark-chip">Des. Base: {{voo.regraTarifaAerea.designadorBase}}</mat-basic-chip>
                  <mat-basic-chip *ngIf="voo.regraTarifaAerea?.familiaTarifa" class="dark-chip">Fam. Tarifa: {{voo.regraTarifaAerea.familiaTarifa}}</mat-basic-chip>
                </div>
              </div>

            </ng-template>

          </mat-expansion-panel>
        </div>

        <ng-container *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.excluido">
          <div *ngIf="data.precificacaoTesteResponse.motivoExclusaoVoos.length > 0" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center">
            <mat-expansion-panel hideToggle fxFlex fxFlexAlign="center">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <div fxLayoutGap="4px" fxFlex fxLayoutAlign="start center" fxLayout="row">
                    <span>Motivos da exclusão </span>
                    <mat-basic-chip class="dark-chip">{{data.precificacaoTesteResponse.motivoExclusaoVoos.length}}</mat-basic-chip>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>

              <ng-template matExpansionPanelContent>
                <ng-container *ngIf="data.precificacaoTesteResponse.motivoExclusaoVoos.length > 0">
                  <div *ngFor="let configComMotivo of data.precificacaoTesteResponse.motivoExclusaoVoos">
                    <div fxLayout="column" fxLayoutGap="4px" fxFlex fxFlexAlign="space-between center" style="margin-bottom: 12px;">
                      <div fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <mat-basic-chip class="red-chip">P{{configComMotivo.prioridade}}</mat-basic-chip>
                        <a target="_blank" (click)="redirect($event, 'pricingSettings', configComMotivo.idConfiguracaoPrecificacao)">
                          <span>{{ configComMotivo.idConfiguracaoPrecificacao }} - {{ configComMotivo.nomeConfiguracaoPrecificacao }}</span>
                        </a>
                      </div>
                      <div *ngFor="let resComparada of configComMotivo.restricoesComparadas" fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <span>{{resComparada.tipoRestricao | captalize}}: </span>
                        <div *ngFor="let r of configComMotivo.restricoesComparadas">
                          <span *ngIf="r.tipoRestricao === resComparada.tipoRestricao">
                            <mat-basic-chip class="red-chip" *ngFor=" let rc of r.restricoesConfig">{{rc}}</mat-basic-chip>
                            <mat-basic-chip class="grey-chip">{{r.operador}}</mat-basic-chip>
                            <mat-basic-chip class="dark-chip" *ngFor=" let rg of r.restricoesGeradas">{{rg}}</mat-basic-chip>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>

              </ng-template>

            </mat-expansion-panel>
          </div>
        </ng-container>

        <ng-container *ngIf="!viewData.precificacaoTesteResponse.resposta.resultadoDebug.excluido">
          <div *ngIf="viewData.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesEMotivos.length > 0" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center">
            <mat-expansion-panel hideToggle fxFlex fxFlexAlign="center">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <div fxLayoutGap="4px" fxFlex fxLayoutAlign="start center" fxLayout="row">
                    <span>Configurações de Precificação Excluídas </span>
                    <mat-basic-chip class="dark-chip">{{ viewData.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesEMotivos.length }}</mat-basic-chip>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>

              <ng-template matExpansionPanelContent>
                <div *ngFor="let configComMotivo of viewData.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesEMotivos; trackBy: trackByConfig">
                  <ng-container *ngIf="configComMotivo">
                    <div fxLayout="column" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center" style="margin-bottom: 40px;">
                      <div fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <mat-basic-chip class="red-chip">P{{configComMotivo.motivoExclusaoVoo.prioridade}}</mat-basic-chip>
                        <a target="_blank" (click)="redirect($event, 'pricingSettings', configComMotivo.motivoExclusaoVoo.idConfiguracaoPrecificacao)">
                          <span>{{ configComMotivo.motivoExclusaoVoo.idConfiguracaoPrecificacao }} - {{ configComMotivo.motivoExclusaoVoo.nomeConfiguracaoPrecificacao }}</span>
                        </a>
                      </div>
                      <div *ngIf="configComMotivo.motivoExclusaoVoo.restricoesNaoGeradas?.length > 0" fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <span>Restrições não geradas:</span>
                        <div *ngFor="let rng of configComMotivo.motivoExclusaoVoo.restricoesNaoGeradas">
                          <mat-basic-chip class="red-chip">{{rng}}</mat-basic-chip>
                        </div>
                      </div>
                      <div *ngIf="configComMotivo.motivoExclusaoVoo.restricoesBarradas.length > 0" fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <span>Restrições barradas:</span>
                        <div *ngFor="let rng of configComMotivo.motivoExclusaoVoo.restricoesBarradas">
                          <mat-basic-chip class="red-chip">{{rng | json}}</mat-basic-chip>
                        </div>
                      </div>
                      <div *ngFor="let r of configComMotivo.motivoExclusaoVoo.restricoesComparadas; trackBy: trackByIndex" fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <span>{{r.tipoRestricao | captalize | noUnderscore}}: </span>
                        <span>
                          <mat-basic-chip class="dark-chip" *ngFor=" let rg of r.restricoesGeradas; trackBy: trackByIndex">{{rg}}</mat-basic-chip>
                          <mat-basic-chip *ngIf="r.tipoAgrupamento" class="blue-chip">{{r.tipoAgrupamento}}</mat-basic-chip>
                          <mat-basic-chip class="grey-chip">{{r.operador}}</mat-basic-chip>
                          <mat-basic-chip class="red-chip" *ngFor=" let rc of r.restricoesConfig; trackBy: trackByIndex">{{rc}}</mat-basic-chip>
                        </span>
                      </div>
                    </div>
                  </ng-container>
                </div>

              </ng-template>

            </mat-expansion-panel>
          </div>
        </ng-container>

        <ng-container *ngIf="!data.precificacaoTesteResponse.resposta.resultadoDebug.excluido">
          <div *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesFeeEMotivos.length > 1" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center">
            <mat-expansion-panel hideToggle fxFlex fxFlexAlign="center">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <div fxLayoutGap="4px" fxFlex fxLayoutAlign="start center" fxLayout="row">
                    <span>Configurações Fee Excluídas </span>
                    <mat-basic-chip class="dark-chip">{{data.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesFeeEMotivos.length - 1}}</mat-basic-chip>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>

              <ng-template matExpansionPanelContent>
                <div *ngFor="let configComMotivo of data.precificacaoTesteResponse.resposta.resultadoDebug.configuracoesFeeEMotivos">
                  <ng-container *ngIf="configComMotivo && data.precificacaoTesteResponse.resposta.resultadoDebug.configuracaoFee.id !== configComMotivo.motivoExclusaoVoo.idConfiguracaoPrecificacao">
                    <div fxLayout="column" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center" style="margin-bottom: 40px;">
                      <div fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <mat-basic-chip class="red-chip">P{{configComMotivo.motivoExclusaoVoo.prioridade}}</mat-basic-chip>
                        <a target="_blank" (click)="redirect($event, 'configuracaofee', configComMotivo.motivoExclusaoVoo.idConfiguracaoPrecificacao)">
                          <span>{{ configComMotivo.motivoExclusaoVoo.idConfiguracaoPrecificacao }} - {{ configComMotivo.motivoExclusaoVoo.nomeConfiguracaoPrecificacao }}</span>
                        </a>
                      </div>
                      <div *ngIf="configComMotivo.motivoExclusaoVoo.restricoesNaoGeradas.length > 0" fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <span>Restrições não geradas:</span>
                        <div *ngFor="let rng of configComMotivo.motivoExclusaoVoo.restricoesNaoGeradas">
                          <mat-basic-chip class="red-chip">{{rng}}</mat-basic-chip>
                        </div>
                      </div>
                      <div *ngFor="let resComparada of configComMotivo.motivoExclusaoVoo.restricoesComparadas" fxLayout="row" fxFlex fxFlexAlign="space-between center">
                        <span>{{resComparada.tipoRestricao | captalize | noUnderscore}}: </span>
                        <div *ngFor="let r of configComMotivo.motivoExclusaoVoo.restricoesComparadas">
                          <span *ngIf="r.tipoRestricao === resComparada.tipoRestricao">
                            <mat-basic-chip class="dark-chip" *ngFor=" let rg of r.restricoesGeradas">{{rg}}</mat-basic-chip>
                            <mat-basic-chip *ngIf="r.tipoAgrupamento" class="blue-chip">{{r.tipoAgrupamento}}</mat-basic-chip>
                            <mat-basic-chip class="grey-chip">{{r.operador}}</mat-basic-chip>
                            <mat-basic-chip class="red-chip" *ngFor=" let rc of r.restricoesConfig">{{rc}}</mat-basic-chip>
                          </span>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>

              </ng-template>

            </mat-expansion-panel>
          </div>
        </ng-container>

        <div *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.restricoesGeradas?.length > 0" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center">
          <mat-expansion-panel hideToggle fxFlex fxFlexAlign="center">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div fxLayoutGap="4px" fxFlex fxLayoutAlign="start center" fxLayout="row">
                  <span>Restrições geradas </span>
                  <mat-basic-chip class="dark-chip">{{data.precificacaoTesteResponse.resposta.resultadoDebug.restricoesGeradas?.length}}</mat-basic-chip>
                </div>
              </mat-panel-title>
            </mat-expansion-panel-header>

            <ng-template matExpansionPanelContent>
              <div fxFlex fxLayout="column">
                <div *ngFor="let restricoes of groupRestrictionsByType(data.precificacaoTesteResponse.resposta.resultadoDebug.restricoesGeradas)" fxFlex fxFlexAlign="space-between center" fxLayout="row wrap">
                  <span>{{restricoes.tipoRestricao | captalize | noUnderscore}}: </span>
                  <mat-basic-chip *ngFor="let v of restricoes.valores" class="dark-chip"><span>{{v.valor}}</span></mat-basic-chip>
                </div>
              </div>
            </ng-template>
          </mat-expansion-panel>
        </div>

        <!-- Futuramente validar se a resposta no front antigo está sendo mapeada de forma correta e se está sendo utilizada -->
        <!-- <div *ngIf="data.precificacaoTesteResponse.resposta.resultadoDebug.restricoesComparadas.length > 0" fxLayoutGap="5px" fxFlex fxFlexAlign="space-between center">
          <mat-expansion-panel hideToggle fxFlex fxFlexAlign="center">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div fxLayoutGap="4px" fxFlex fxLayoutAlign="start center" fxLayout="row">
                  <mat-icon style="color: green">check</mat-icon>
                  <span>Restrições geradas e comparadas </span>
                  <mat-basic-chip class="dark-chip">{{data.precificacaoTesteResponse.resposta.resultadoDebug.restricoesComparadas.length}}</mat-basic-chip>
                </div>
              </mat-panel-title>
            </mat-expansion-panel-header>

            <ng-template matExpansionPanelContent>
              <div fxFlex fxLayout="column">
                <div *ngFor="let restricoes of groupRestrictionsByType(data.precificacaoTesteResponse.resposta.resultadoDebug.restricoesComparadas)" fxFlex fxFlexAlign="space-between center" fxLayout="row">
                  <span>{{restricoes.tipoRestricao | captalize | noUnderscore}}: </span>
                  <mat-basic-chip *ngFor="let v of restricoes.valores" class="dark-chip">{{v.valor}}</mat-basic-chip>
                </div>
              </div>
            </ng-template>
          </mat-expansion-panel>
        </div> -->

        <ng-container *ngIf="!data.precificacaoTesteResponse.resposta.resultadoDebug.excluido">
          <div *ngFor="let item of data.precificacaoTesteResponse.resposta.valoresCalculados | keyvalue" fxLayout="column" fxLayoutGap="20px" fxFlex fxFlexAlign="space-between center">
            <span style="padding-top: 10px;">Passageiro: {{item.key}}</span>
            <mat-divider></mat-divider>
            <div fxFlex fxLayout="row">
              <span>Tarifa</span>
              <mat-basic-chip class="dark-chip">Vr Neto {{item.value.tarifa.valorNeto | currency: item.value.moeda + ' '}}</mat-basic-chip>
              <mat-basic-chip class="dark-chip">Vr Desc. {{item.value.tarifa.valorDesconto | currency: item.value.moeda + ' '}}</mat-basic-chip>
            </div>
            <mat-divider></mat-divider>
            <div fxFlex fxLayout="row">
              <span>Tarifa Original</span>
              <mat-basic-chip class="dark-chip">Vr Neto {{item.value.tarifaOriginal.valor.valorNeto | currency: item.value.moeda + ' '}}</mat-basic-chip>
              <mat-basic-chip class="dark-chip">Vr Desc. {{item.value.tarifaOriginal.valor.valorDesconto | currency: item.value.moeda + ' '}}</mat-basic-chip>
            </div>
            <mat-divider></mat-divider>
            <div fxFlex fxLayout="row">
              <span>Valores Sobretaxas</span>
              <mat-basic-chip class="dark-chip">TAXA BOLSA {{item.value.valoresSobretaxas.TAXA_BOLSA | currency: item.value.moeda + ' '}}</mat-basic-chip>
              <mat-basic-chip class="dark-chip">MARKUP {{item.value.valoresSobretaxas.MARKUP | currency: item.value.moeda + ' '}}</mat-basic-chip>
              <mat-basic-chip class="dark-chip">FEE {{item.value.valoresSobretaxas.FEE | currency: item.value.moeda + ' '}}</mat-basic-chip>
              <mat-basic-chip class="dark-chip">RAV {{item.value.valoresSobretaxas.RAV | currency: item.value.moeda + ' '}}</mat-basic-chip>
            </div>
            <mat-divider></mat-divider>
            <div fxFlex fxLayout="row">
              <span>Valores Taxas</span>
              <mat-basic-chip class="dark-chip">TAXA Embarque {{item.value.valoresTaxas.TAXAEMBARQUE | currency: item.value.moeda + ' '}}</mat-basic-chip>
              <mat-basic-chip class="dark-chip" *ngIf="item.value.valoresTaxas.DU">DU {{item.value.valoresTaxas.DU | currency: item.value.moeda + ' '}}</mat-basic-chip>
            </div>
          </div>
        </ng-container>




      </div>

    </mat-list-item>

    <div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
      <button [mat-dialog-close]="null" mat-stroked-button aria-label="Fechar" type="button">
        <mat-icon>close</mat-icon>
      </button>
    </div>
</div>
