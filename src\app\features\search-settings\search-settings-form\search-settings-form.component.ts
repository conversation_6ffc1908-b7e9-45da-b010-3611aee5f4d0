import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {FormArray, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {Restangular} from 'ngx-restangular';
import {AuthService} from '../../../core/services/auth.service';
import {RedirectService} from '../../../core/services/redirect.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../../shared/components/snack-bar/snack-bar.component';
import {DialogData} from '../../../shared/components/dialog/DialogData';
import {MatDialog, MatDialogRef} from '@angular/material/dialog';
import {DialogComponent} from '../../../shared/components/dialog/dialog.component';
import {SimpleHistoryComponent} from 'src/app/shared/components/simple-history/simple-history.component';
import {AcordoComercialDialogComponent} from "../acordo-comercial-dialog/acordo-comercial-dialog.component";
import { NgxObjectDiffService } from 'ngx-object-diff';


const ACORDO_COMERCIAL = {
  sistEmis: [null],
  chamadas: [[]],
};

@Component({
  selector: 'app-search-settings-form',
  templateUrl: './search-settings-form.component.html',
  styleUrls: ['./search-settings-form.component.scss']
})
export class SearchSettingsFormComponent implements OnInit {

  public get isNew(): boolean {
    return !(this.currentId !== null && typeof this.currentId !== 'undefined');
  }

  public get getId(): number {
    return this.currentId;
  }

  public get isActive(): boolean {
    return this.active;
  }

  public get acordoComerciaisAgrupados() {
    return this.searchSettingsFormGroup.controls.acordosComerciais as FormArray;
  }

  public get cloneId() {
    let cloneId;
    this.route.queryParams.subscribe((params) => {
      cloneId = params.cloneId;
    });
    return cloneId;
  }

  loadingSearch = false;

  constructor(
    private _formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private restangular: Restangular,
    private _snackBar: MatSnackBar,
    private authService: AuthService,
    private redirectService: RedirectService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private objectDiff: NgxObjectDiffService,
    private _dialog: MatDialog,) {

  }

  public readonly acordoComercial = ACORDO_COMERCIAL;

  @ViewChild('dialog')
  public formDialog: TemplateRef<any>;

  public loading = true;
  public acordoComercialLoading = false;
  public active: boolean = undefined;
  public title: string;
  public groupOfCias: boolean;
  public ciasFromGroup: any[];
  public dialogFormControl: any;
  private agrupaChamadaUnica: any = true;
  private acordosComerciaisIds: any[] = [];

  protected readonly RESOURCE_NAME: string = 'configuracoesBusca';

  private currentId: number = undefined;
  private initialized = false;
  searchSettingsFormGroup: FormGroup;


  data = {
    nome: null,
    prioridade: null,
    peso: null,
    empresa: null,
    nacInt: null,
    produtos: [],
    filiais: [],
    agrupaChamadaUnica: null,
    bestPrice: false,
    restricoes: [],
    historico: [],
    ativo: null,
    acordosComerciais: [],
    ciasExcluidas: [],
    ciasValidadorasExcluidas: [],
    editavel: null,
    statusPublicacao: null,
    idOrigem: null,
    idConfigEditavel: null,
  };

  dialogRef: MatDialogRef<SearchSettingsFormComponent>;

  public async ngOnInit() {
    this.searchSettingsFormGroup = this._formBuilder.group({
      nome: [this.data ? this.data.nome : null, [Validators.required]],
      prioridade: [this.data ? this.data.prioridade : null, [Validators.required]],
      peso: [this.data ? this.data.peso : null],
      empresa: [this.data ? this.data.empresa : null, [Validators.required]],
      nacInt: [this.data ? this.data.nacInt : null, [Validators.required]],
      produtos: [this.data ? this.data.produtos : null],
      filiais: [this.data ? this.data.filiais : null],
      agrupaChamadaUnica: [this.data ? this.data.agrupaChamadaUnica : null],
      bestPrice: [this.data ? (this.data.bestPrice ?? false) : false],
      restricoes: [this.data ? this.data.restricoes : null],
      historico: [this.data.historico ? this.data.historico : []],
      editavel: [this.data ? this.data.editavel : null],
      ativo: [this.data ? this.data.ativo : null],
      acordosComerciais: this._formBuilder.array([]),
      ciasExcluidas: [this.data ? this.data.ciasExcluidas : null],
      ciasValidadorasExcluidas: [this.data ? this.data.ciasValidadorasExcluidas : null],
      idConfigEditavel: [this.data ? this.data.idConfigEditavel : null],
      statusPublicacao: [this.data ? this.data.statusPublicacao : null],
    });
    this.dialogFormControl = this._formBuilder.group({acordoComercial: ''});

    this.currentId = this.route.snapshot.params.id;

    this.loading = false;
    this.initialized = true;

    if (!isNaN(this.currentId)) {
      this.loading = true;
      try {
        await this.loadItem(this.currentId);
      } finally {
        this.loading = false;
      }
    } else if (!isNaN(this.cloneId)) {
      this.loading = true;
      try {
        await this.loadClone(this.cloneId);
      } finally {
        this.loading = false;
      }
    }
  }

  protected async loadItem(id: number): Promise<void> {
    const response = await this.findById(id);
    await this.setValueItem(response);
  }

  protected async loadClone(id: any): Promise<void> {
    const response = await this.findCloneById(id);

    await this.setValueItem(response);
  }

  protected async setValueItem(data: any): Promise<void> {

    const {
      nome,
      prioridade,
      peso,
      empresa,
      nacInt,
      produtos,
      filiais,
      agrupaChamadaUnica,
      bestPrice,
      restricoes,
      historico,
      ativo,
      editavel,
      idConfigEditavel,
      acordosComerciais,
      ciasExcluidas,
      ciasValidadorasExcluidas,
      statusPublicacao,
    } = data.body;
    this.data = data.body;

    this.agrupaChamadaUnica = agrupaChamadaUnica;
    this.acordosComerciaisIds = acordosComerciais.map(acordo => acordo.id);
    const acordosComerciaisAgrupados = await this.updateAcordoComerciais(this.acordosComerciaisIds, agrupaChamadaUnica);
    this.currentId = data.body.id;

    this.acordoComercialLoading = true;
    this.active = ativo;
    this.title = nome;
    this.searchSettingsFormGroup.setValue({
      nome,
      prioridade,
      peso,
      empresa,
      nacInt,
      produtos,
      filiais,
      agrupaChamadaUnica,
      bestPrice,
      restricoes,
      historico: historico ? historico : [],
      editavel,
      idConfigEditavel,
      ativo,
      acordosComerciais: acordosComerciaisAgrupados,
      ciasExcluidas,
      ciasValidadorasExcluidas,
      statusPublicacao,
    });
  }

  protected async findCloneById(cloneId): Promise<void> {
    return this.restangular
      .one(this.RESOURCE_NAME, cloneId)
      .customGET('clonar').toPromise();
  }

  protected async findAcordosComerciaisPorChamada(ids: any, chamadaUnica: any) {
    return this.restangular.all('acordosComerciaisPorChamada').getList({
      agrupaChamadaUnica: chamadaUnica,
      ids: ids.join(','),
    }).toPromise();
  }

  public async clone() {
    this.loading = true;
    try {
      this.router.navigate(['searchSettings' + '/' + 'criar'], {queryParams: {cloneId: this.currentId}});
      this.findCloneById(this.currentId).then(response => {
        this.currentId = null;
        this.setValueItem(response);
      });
    } finally {
      this.loading = false;
    }
  }

  private findById(id: number): Promise<any> {
    return this.restangular.one(this.RESOURCE_NAME, id).get().toPromise();
  }

  public onSubmit() {
    this.loadingSearch = true;
   if (this.searchSettingsFormGroup.controls.ativo.value == false ){
    this.loadingSearch = false;
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Configuração de busca Inativa,  Restaure antes para poder ser editada',
          buttons: [{label: "Fechar", action: "close", color: "warn"}]
        }
      });
    } else {
      try {
        if (this.searchSettingsFormGroup.valid) {
          let request: any;
          const value = this.searchSettingsFormGroup.value;
          value.ativo = true;
          value.agrupaChamadaUnica = value.agrupaChamadaUnica ? value.agrupaChamadaUnica : false;
          value.bestPrice = !!value.bestPrice;
          value.acordosComerciais = this.convertToAcordoComercial();
          this.insertRestrictions(value);
          if (this.currentId != null) {
            request = this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT(value);
          } else {
            request = this.restangular.all(this.RESOURCE_NAME).post(value);
          }
          request.subscribe(response => {
            this.router.navigateByUrl('/searchSettings');
          }, err => {
            this._snackBar.openFromComponent(SnackBarComponent, {
              data: {
                httpErrorResponse: err,
                buttons: [
                  {label: 'Mais informações', action: 'information', color: 'warn'},
                  {label: 'Fechar', action: 'close', color: 'warn'},
                ],
              }
            });
            this.loadingSearch = false;
            this.loading = false;
          });
        } else {
          this.loadingSearch = false;
          this.searchSettingsFormGroup.markAllAsTouched();
          this._snackBar.openFromComponent(SnackBarComponent, {
            data: {
              title: 'Alerta',
              subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
              buttons: [{label: 'Fechar', action: 'close', color: 'warn'}]
            }
          });
        }

      } finally {
        this.loading = false;
      }
    }
  }


  private insertRestrictions(value: any) {
    value.restricoes = [];
    value.restricoes = this.data.restricoes;
  }

  public showHistory(event: Event): void {
    event.preventDefault();
    this.data.historico.forEach((element, index) => {
      if(index === (this.data.historico.length - 1)){
        this.feedHist(element, (diff) => {
          element.diff = diff;
          const dialogRef = this.dialog.open(SimpleHistoryComponent, {
            autoFocus: false,
            maxHeight: '350px',
            width: '700px',
            data: this.data.historico,
          });
        });
      }else {
        this.feedHist(element, (diff) => {
          element.diff = diff;
        });
      }
    });
  }

  public async feedHist(hist, callback) {
    if (hist.configBusca.diff === undefined) {
      await this.restangular.one(this.RESOURCE_NAME, hist.configBusca.id).get()
        .subscribe(response => {
          let body = response.body;
          let historico = camposComparaveisConfiguracao(body);
          let atual = camposComparaveisConfiguracao(this.data);

          if(!historico.editavel && !historico.ativo && !atual.ativo){
            historico.ativo = true;
          }
          let diff = this.objectDiff.diff(historico, atual);
          hist.configBusca.diff = diff;
          callback(this.objectDiff.toJsonDiffView(diff));
        });
    }

    function camposComparaveisConfiguracao(data) {

      return {
        nome: data.nome,
        prioridade: data.prioridade,
        editavel: data.editavel,
        empresa: data.empresa,
        nacInt: data.nacInt,
        produtos: data.produtos,
        filiais: data.filiais,
        agrupaChamadaUnica: data.agrupaChamadaUnica,
        bestPrice: !!data.bestPrice,
        ativo: data.ativo,
        ciasExcluidas: data.ciasExcluidas,
        ciasValidadorasExcluidas: data.ciasValidadorasExcluidas,
        restricoes: camposComparaveisRestricao(data.restricoes),
        statusPublicacao: data.statusPublicacao,
      }
    }

    function camposComparaveisRestricao(restricoes) {
      return restricoes.map(restricao =>
        ({
          tipoOperador: restricao.tipoOperador,
          tipoAgrupamento: restricao.tipoAgrupamento,
          tipoRestricao: restricao.tipoRestricao,
          valores: (restricao.valores)
            ? restricao.valores
              .sort((a, b) => (a.valor < b.valor) ? -1 : (a.valor > b.valor) ? 1 : 0)
              .map(valor => ({tipoRestricao: valor.tipoRestricao, valor: valor.valor}))
            : restricao.valores,
          restricoes: camposComparaveisRestricao(restricao.restricoes),
        })
      )
    }
  }

  public restoreItemDialog(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: 'Alerta',
        message: "Deseja restaurar o registro? ao ser restaurado o status da configuração será NÃO PUBLICADO",
        buttons: [
          { label: 'Não', action: 'close', color: 'cancel' },
          {
            label: 'Sim',
            action: (confirm) => {
              this.restoreItem();
              confirm();
            },
            color: 'save',
          },
        ],
        align: 'end',
      },
      () => this.restoreItem()
    );
  }

  public async restoreItem() {
    this.loading = true;
    try {
      await this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT({}, 'ativar').toPromise();
      await this.loadItem(this.currentId);
    } catch (err) {
      this._snackBar.openFromComponent(SnackBarComponent, {
        data: {
          httpErrorResponse: err,
          buttons: [
            {label: 'Mais informações', action: 'information', color: 'warn'},
            {label: 'Fechar', action: 'close', color: 'warn'}]
        }
      });
    } finally {
      this.loading = false;
    }
  }

  public removeItem(event: Event) {
    event.preventDefault();
    this.showConfirmationDialog(
      {
        title: 'Alerta',
        message: 'Deseja inativar o registro?',
        buttons: [{label: 'Não', action: 'close', color: 'cancel'},
          {
            label: 'Sim', action: (confirm) => {
              this.deleteItem();
              confirm();
            }, color: 'save'
          }],
        align: 'end'
      }, () => this.deleteItem());
  }

  private async deleteItem() {
    this.loading = true;
    try {
      await this.restangular.one(this.RESOURCE_NAME, this.currentId).remove().toPromise();
      if (this.data.statusPublicacao === 'NAO_PUBLICADA') {
        await this.router.navigateByUrl("/searchSettings");
      } else {
        await this.loadItem(this.currentId);
      }
    } catch (error) {
      this._snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: error,
              buttons: [
                {label: 'Mais informações', action: 'information', color: 'warn'},
                {label: 'Fechar', action: 'close', color: 'warn'},
              ],
            }
          })
    } finally {
      this.loading = false;
    }
  }

  private showConfirmationDialog(data: DialogData, action: Function): void {
    const dialogRef = this._dialog.open(DialogComponent, {
      maxWidth: '400px',
      data
    });
  }


  public ciasExcluidasValue2Form() {
    return ({ciaExcluida, sistEmis}: {
      ciaExcluida: any,
      sistEmis: string,
    }) => ({
      ciaExcluida,
      sistEmis,
    });
  }

  public form2ciasExcluidasValue({ciaExcluida, sistEmis}: { ciaExcluida: any, sistEmis: string }) {
    return ({ciaExcluida, sistEmis});
  }

  public ciasValidadorasExcluidasValue2Form() {
    return ({ciaExcluida, sistEmis}: {
      ciaExcluida: any,
      sistEmis: string,
    }) => ({
      ciaExcluida,
      sistEmis,
    });
  }

  public form2ciasValidadorasExcluidasValue() {
    return ({ciaExcluida, sistEmis, ciaValidadora = true}: {
      ciaExcluida: any,
      sistEmis: string,
      ciaValidadora: boolean
    }) => ({
      ciaExcluida,
      sistEmis,
      ciaValidadora,
    });
  }

  public addExcludedCias(value: any[]) {
    if (this.groupOfCias) {
      const lastElement = value[value.length - 1];
      const ciasFromGroupFormatted = this.ciasFromGroup.map((cia) => {
        return {ciaExcluida: cia, sistEmis: lastElement.sistEmis};
      });
      this.searchSettingsFormGroup.value.ciasExcluidas.pop();
      this.searchSettingsFormGroup.value.ciasExcluidas = [...this.searchSettingsFormGroup.value.ciasExcluidas, ...ciasFromGroupFormatted];
      this.groupOfCias = false;
    }
  }

  public addExcludedValidatorCias(value: any[]) {
    if (this.groupOfCias) {
      const lastElement = value[value.length - 1];
      const ciasFromGroupFormatted = this.ciasFromGroup.map((cia) => {
        return {ciaExcluida: cia, sistEmis: lastElement.sistEmis, ciaValidadora: true};
      });
      this.searchSettingsFormGroup.value.ciasValidadorasExcluidas.pop();
      this.searchSettingsFormGroup.value.ciasValidadorasExcluidas = [...this.searchSettingsFormGroup.value.ciasValidadorasExcluidas, ...ciasFromGroupFormatted];
      this.groupOfCias = false;
    }
  }

  public verifyIfGroupOfCias(value: any) {
    if (value.ciasAereas) {
      this.groupOfCias = true;
      this.ciasFromGroup = value.ciasAereas;
    }
  }

  public showSaveAndPublishDialog(event: Event) {
    event.preventDefault();
    this.loadingSearch = true;
    if (this.searchSettingsFormGroup.controls.ativo.value == false ){
      this.loadingSearch = false;
      this.snackBar.openFromComponent(SnackBarComponent, {
        data: {
          title: 'Alerta',
          subTitle: 'Configuração de busca Inativa,  Restaure antes para poder ser editada',
          buttons: [{label: "Fechar", action: "close", color: "warn"}]
        }
      });
    }  else {
      this.showConfirmationDialog({
        title: 'Salvar e Publicar Configuração',
        message: 'Tem certeza de que deseja salvar e publicar este item?',
        buttons: [{label: 'Cancelar', action: 'close', color: 'cancel'},
          {
            label: 'Sim', action: (confirm) => {
              this.saveAndPublish();
              confirm();
            }, color: 'save'
          }],
        align: 'end'
      }, () => this.saveAndPublish());
    }
  }

  public saveAndPublish() {
    this.loading = true;
    try {
      if (this.searchSettingsFormGroup.valid) {
        let request: any;
        const value = this.searchSettingsFormGroup.value;
        value.ativo = true;
        value.agrupaChamadaUnica = value.agrupaChamadaUnica ? value.agrupaChamadaUnica : false;
        value.bestPrice = !!value.bestPrice;
        value.acordosComerciais = this.convertToAcordoComercial();
        this.insertRestrictions(value);
        if (this.currentId != null) {
          request = this.restangular.one(this.RESOURCE_NAME, this.currentId).customPUT(value);
        } else {
          request = this.restangular.all(this.RESOURCE_NAME).post(value);
        }
        request.subscribe(async response => {
          const {id} = response.body;
          await this.restangular.one(this.RESOURCE_NAME, id).customPUT({}, 'publicar').subscribe((response) => {
            this.router.navigateByUrl('/searchSettings');
          });
        }, err => {
          this._snackBar.openFromComponent(SnackBarComponent, {
            data: {
              httpErrorResponse: err,
              buttons: [
                {label: 'Mais informações', action: 'information', color: 'warn'},
                {label: 'Fechar', action: 'close', color: 'warn'},
              ],
            }
          });
          this.loadingSearch = false;
          this.loading = false;
        });
      } else {
        this.searchSettingsFormGroup.markAllAsTouched();
        this.loadingSearch = false;
        this._snackBar.openFromComponent(SnackBarComponent, {
          data: {
            title: 'Alerta',
            subTitle: 'Verifique se todos os campos estão preenchidos corretamente',
            buttons: [{label: 'Fechar', action: 'close', color: 'warn'}]
          }

        });
      }

    } finally {
      this.loading = false;
    }
  }

  addAcordoComercial(event) {
    event.preventDefault();
    const dialogRef = this._dialog.open(AcordoComercialDialogComponent);
    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.acordosComerciaisIds.push(result.acordoComercial.id);
        const acordoComerciaisAgrupados = await this.updateAcordoComerciais([...new Set(this.acordosComerciaisIds)], this.agrupaChamadaUnica);
        this.acordoComerciaisAgrupados.patchValue(acordoComerciaisAgrupados);
        this.acordoComercialLoading = true;
      }
    });
  }

  public async updateAcordoComerciais(acordosComerciais: any[], agrupaChamadaUnica) {
    const acordosComerciaisAgrupadosResponse = await this.findAcordosComerciaisPorChamada(acordosComerciais, agrupaChamadaUnica);
    const acordosComerciaisAgrupados = acordosComerciaisAgrupadosResponse.body.map(
      acordo => {
        return {
          sistEmis: acordo.sistEmis,
          chamadas: acordo.chamadas,
        };
      }
    );

    while (this.acordoComerciaisAgrupados.length > 0) {
      this.acordoComerciaisAgrupados.removeAt(0);
    }
    while (this.acordoComerciaisAgrupados.length < acordosComerciaisAgrupados.length) {
      this.acordoComerciaisAgrupados.push(this._formBuilder.group(this.acordoComercial));
    }
    return acordosComerciaisAgrupados;
  }

  public convertToAcordoComercial() {
    const acordoComercial = [];
    this.acordoComerciaisAgrupados.value.flatMap(acordo => {
      acordo.chamadas.forEach(chamadas => {
        chamadas.acordosComerciais.map(acordos => {
          acordoComercial.push({
            id: acordos.id,
            nome: acordos.nome,
            ciasAereas: acordos.ciasAereas,
            ativo: acordos.ativo,
          });
        })
      })
    })
    return acordoComercial;
  }

  removeAcordoComercial(event, acordo) {
    this.acordoComerciaisAgrupados.value.map((acordoAgrupado, acordoIndex) => {
      acordoAgrupado.chamadas.forEach((chamadas, index) => {
        const element = chamadas.acordosComerciais.findIndex(element => element.id === acordo.id);
        if (element >= 0) {
          chamadas.acordosComerciais.splice(element, 1);
        }
        if (chamadas.acordosComerciais.length === 0) {
          acordoAgrupado.chamadas.splice(index, 1);
        }
      });
    })
  }
}
