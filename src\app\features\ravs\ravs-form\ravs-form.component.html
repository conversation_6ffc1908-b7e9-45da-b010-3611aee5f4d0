<div>
    <div *ngIf="loading">
        <mat-progress-bar mode="query" color="warn"></mat-progress-bar>
    </div>
    <form class="mn-card" [formGroup]="ravFormGroup" (ngSubmit)="onSubmit()" fxLayoutGap="15px">
        <a class="back-button" routerLink="/ravs">
            <span>Configuracao Rav</span>
        </a>

        <div fxLayout="row" fxLayoutAlign="space-between center" class="topbar custom-dark-theme">
            <div fxLayout="row" *ngIf="!isNew" fxLayoutGap="5px">
              <h2>{{ title }}</h2>
              <mat-basic-chip class="active" *ngIf="data.statusPublicacao === 'PUBLICADA'">Publicada</mat-basic-chip>
              <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'NAO_PUBLICADA'">Não publicada</mat-basic-chip>
              <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'EM_HISTORICO'">Histórico</mat-basic-chip>
              <mat-basic-chip class="inactive" *ngIf="data.statusPublicacao === 'INATIVO'">Inativo</mat-basic-chip>
            </div>
            <div fxLayout="row" *ngIf="isNew">
                <h2>Nova Configuração Rav</h2>
            </div>
            <button *ngIf="!isNew && data.historico.length" mat-stroked-button (click)="showHistory($event)">
                <mat-icon>access_time</mat-icon>
            </button>
        </div>
        <div fxLayout="column" fxLayoutGap="15px">
            <mat-card>
                <mat-card-content>
                    <div fxLayout="row" fxLayoutGap="15px">
                      <app-input-field fxFlex="80" name="nome" label="Nome" required="true" maxlength="50"></app-input-field>
                        <app-input-field fxFlex="20" type="number" name="prioridade" label="Prioridade" required="true">
                        </app-input-field>
                    </div>
                    <div fxLayout="row" fxLayoutGap="15px">
                        <app-search-box fxFlex="100" formControlName="tipoTarifaAcordo" [options]="[{id:'AMBAS', nome: 'Ambas'},
                                    {id:'PUBLICA', nome: 'Pública'},
                                    {id: 'PRIVADA', nome: 'Privada'}]" displayField="nome" valueField="id"
                                    placeholder="Tipo de tarifa" valueAsValueField="true" [required]="true">
                        </app-search-box>
                        <app-search-box fxFlex="100" formControlName="tipoConfigRav" [options]="[{id:'GRUPO', nome: 'Grupo'},
                                    {id:'PADRAO', nome: 'Padrão'}]" displayField="nome" valueField="id"
                                    placeholder="Tipo de Config" valueAsValueField="true" [required]="true">
                        </app-search-box>
                        <app-search-box fxFlex="100" formControlName="nacInt" displayField="nome"
                                    [required]="true" valueField="id" placeholder="Nacional/Internacional" valueAsValueField="true"
                                    [options]="[{id: 'AMBOS', nome: 'Ambos'},
                                                {id: 'NAC', nome: 'Nacional'},
                                                {id: 'INT', nome: 'Internacional'}]">
                        </app-search-box>
                    </div>
                    <div fxLayout="row" fxLayoutGap="15px">
                        <app-search-box fxFlex="100" formControlName="produtos" resource="produtos" displayField="nome"
                            [searchFields]="['nome']" [required]="true" valueField="id" placeholder="Produtos"
                            [multiple]="true">
                        </app-search-box>
                    </div>
                    <div fxLayout="row" fxLayoutGap="15px">
                        <app-search-box fxFlex="100" formControlName="empresas" resource="empresas" displayField="nome"
                            [searchFields]="['referencia', 'nome']" [required]="true" valueField="id"
                            placeholder="Empresa" [multiple]="true">
                        </app-search-box>
                    </div>
                    <div *ngIf="ravFormGroup.value.tipoConfigRav && ravFormGroup.value.tipoConfigRav === 'GRUPO'" fxLayout="row" fxLayoutGap="15px">
                        <app-search-box fxFlex="100" formControlName="grupos" resource="grupos" displayField="nomeTratado"
                            [searchFields]="['nome']" valueField="id"
                            placeholder="Grupos" [multiple]="true">
                        </app-search-box>
                    </div>
                </mat-card-content>
            </mat-card>
        </div>
        <p></p>
        <div>
            <!-- Tabela customizada com linhas expandidas intercaladas -->
            <mat-card>
                <mat-card-title>
                    <div fxLayout="row">
                        <div fxFlex>Cias Aereas</div>
                        <button mat-icon-button type="button" (click)="addCiaAerea(); $event.preventDefault(); $event.stopPropagation();"
                                matTooltip="Adicionar linha individual">
                            <mat-icon>add</mat-icon>
                        </button>
                        <button mat-icon-button type="button" (click)="openBulkCreateModal(); $event.preventDefault(); $event.stopPropagation();"
                                matTooltip="Cadastro em massa">
                            <mat-icon>library_add</mat-icon>
                        </button>
                    </div>
                </mat-card-title>
                <mat-card-content>
                    <div style="border: 1px solid #e0e0e0; border-radius: 4px;">
                        <div style="display: flex; background-color: #fafafa; border-bottom: 1px solid #e0e0e0; font-weight: 500; padding: 12px;">
                            <div style="flex: 1; padding: 0 8px;">CiaAerea</div>
                            <div style="flex: 1; padding: 0 8px;">Nacional/Internacional</div>
                            <div style="flex: 1; padding: 0 8px;">Moeda</div>
                            <div style="flex: 1; padding: 0 8px;">Percentual</div>
                            <div style="flex: 1; padding: 0 8px;">Valor Minimo</div>
                            <div style="flex: 1; padding: 0 8px;">Variavel</div>
                            <div style="width: 80px; padding: 0 8px;">Ações</div>
                        </div>

                        <div *ngFor="let form of ciasAereasFormArray.controls; let i = index">
                            <div [formGroup]="form" style="display: flex; border-bottom: 1px solid #e0e0e0; padding: 12px; background-color: white;">
                                <div style="flex: 1; padding: 0 8px;">
                                    <app-search-box [resource]="getCiasEndpoint()" [formControl]="form.get('ciaAerea')" displayField="nome"
                                        valueField="id" [searchFields]="['codigo', 'nome']" placeholder="Cia Aerea" fxFlex="100"
                                        [disabled]="hasBlockId(form)"
                                        (selectionChange)="onCiaAereaSelectionChange($event, form, i)">
                                    </app-search-box>
                                </div>

                                <div style="flex: 1; padding: 0 8px;">
                                    <app-search-box [formControl]="form.get('nacInt')" [options]="getStatusOptionsForFormIndex(i)"
                                        displayField="nome" valueField="id"
                                        placeholder="Status" fxFlex="100" valueAsValueField="true"
                                        [disabled]="hasBlockId(form) || !form.get('ciaAerea')?.value">
                                    </app-search-box>
                                </div>

                                <div style="flex: 1; padding: 0 8px;">
                                    <app-search-box [formControl]="form.get('moeda')" [options]="[{id:'ARS', nome: 'ARS'},
                                              {id:'BRL', nome: 'BRL'},
                                              {id:'USD', nome: 'USD'}]" displayField="nome" valueField="id" placeholder="Moeda"
                                        fxFlex="100" valueAsValueField="true"
                                        [disabled]="hasBlockId(form)">
                                    </app-search-box>
                                </div>

                                <div style="flex: 1; padding: 0 8px;">
                                    <app-input-field type="percentage" fxFlex="100" [control]="form.get('percentualRav')" label="Porcentagem">
                                    </app-input-field>
                                </div>

                                <div style="flex: 1; padding: 0 8px;">
                                    <app-input-field type="monetary" fxFlex="100" [control]="form.get('valorMinimoRav')" label="Valor Minimo">
                                    </app-input-field>
                                </div>

                                <div style="flex: 1; padding: 0 8px;">
                                    <mat-checkbox formControlName="variavel" fxFlex="25" fxFlexAlign="center"
                                        [disabled]="hasBlockId(form)">
                                        Variavel
                                    </mat-checkbox>
                                </div>

                                <div style="width: 120px; padding: 0 8px;">
                                    <button *ngIf="isBlockRecord(i)"
                                            type="button"
                                            mat-icon-button
                                            (click)="openBulkEditModal(i); $event.preventDefault(); $event.stopPropagation();"
                                            matTooltip="Editar em massa"
                                            matTooltipPosition="above"
                                            color="primary">
                                        <mat-icon>edit</mat-icon>
                                    </button>

                                    <button type="button" mat-icon-button (click)="removeCiaAerea(i); $event.preventDefault(); $event.stopPropagation();" matTooltip="Remover" matTooltipPosition="above">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </div>
                            </div>

                            <div *ngIf="isTodaAsCiasSelected(form)"
                                 style="padding: 16px; background-color: #f9f9f9; border-bottom: 1px solid #e0e0e0;">
                                <div fxLayout="column" fxLayoutGap="12px">
                                    <div style="font-size: 14px; font-weight: 500; color: #666; margin-bottom: 8px;">
                                        Exceções para "Todas as Cias" (Linha {{i + 1}})
                                    </div>

                                    <app-search-box resource="ciasAereas/combo" [formControl]="getExceptionsControl(form)"
                                        displayField="nome" valueField="id" [searchFields]="['codigo', 'nome']"
                                        placeholder="Selecione uma cia para adicionar às exceções" fxFlex="100"
                                        (selectionChange)="onExceptionSelectionChange($event, form)">
                                    </app-search-box>

                                    <div *ngIf="getExceptions(form).length > 0" fxLayout="row wrap" fxLayoutGap="8px" style="margin-top: 8px;">
                                        <mat-chip-list>
                                            <mat-chip *ngFor="let exception of getExceptions(form)"
                                                [removable]="true" (removed)="removeException(exception, form)"
                                                color="primary" selected>
                                                {{exception.nome}}
                                                <mat-icon matChipRemove>cancel</mat-icon>
                                            </mat-chip>
                                        </mat-chip-list>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <mat-error *ngIf="ciasAereasFormArray.touched && ciasAereasFormArray.errors?.['sum']">
                        Percentual deve totalizar 100.
                    </mat-error>
                    <mat-error *ngIf="ciasAereasFormArray.touched && ciasAereasFormArray.errors?.['duplicate']">
                        Sistema emissor duplicado.
                    </mat-error>
                </mat-card-content>
            </mat-card>
        </div>

        <div fxLayout="row" fxLayoutGap="15px" fxLayoutAlign="space-between center">
        </div>
        <div fxLayout="row" fxLayoutAlign="end" class="topbar custom-dark-theme">
          <button *ngIf="isActive && !isNew && isActive !== undefined && data.statusPublicacao !== 'EM_HISTORICO'" color="warn" mat-flat-button
                (click)="removeItem($event)">Inativar</button>
            <button *ngIf="!isActive && !isNew" color="warn" mat-flat-button
                (click)="restoreItemDialog($event)">Restaurar</button>
            <button *ngIf="!isNew" type="button" color="accent" mat-flat-button (click)="clone()">Clonar</button>
            <button mat-flat-button type="button" class="btn btn-primary" color="cancel" routerLink="/ravs">Cancelar
            </button>
            <button mat-flat-button type="submit" class="btn btn-primary" color="save">Salvar</button>
            <button type="button" style="background-color:slateblue" mat-flat-button
        (click)="showSaveAndPublishDialog($event)">Salvar e Publicar</button>
        </div>
    </form>
</div>

<div class="modal-overlay" *ngIf="showBulkModal" (click)="closeBulkModal()">
    <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
            <h3>Cadastro em Massa - Cias Aéreas</h3>
            <button mat-icon-button (click)="closeBulkModal()">
                <mat-icon>close</mat-icon>
            </button>
        </div>

        <form [formGroup]="bulkFormGroup" class="modal-body">
            <div class="form-section">
                <label class="section-label">1. Selecione o Status:</label>

                <div class="form-row">
                    <app-search-box
                        formControlName="nacInt"
                        [options]="[{id:'NAC', nome: 'Nacional'}, {id:'INT', nome: 'Internacional'}, {id:'AMBOS', nome: 'Ambos'}]"
                        displayField="nome"
                        valueField="id"
                        placeholder="Nacional/Internacional"
                        valueAsValueField="true"
                        [required]="true"
                        (selectionChange)="onBulkStatusChange($event)">
                    </app-search-box>
                </div>
            </div>

            <div class="form-section">
                <label class="section-label">2. Selecionar Cias Aéreas:</label>

                <div *ngIf="!bulkFormGroup.get('nacInt')?.value" class="info-message">
                    <mat-icon>info</mat-icon>
                    <span>Selecione primeiro o status (Nacional/Internacional/Ambos) para habilitar a seleção de cias aéreas.</span>
                </div>

                <app-search-box
                    *ngIf="bulkFormGroup.get('nacInt')?.value && bulkCiasEndpointKey"
                    [resource]="bulkCiasEndpointKey"
                    formControlName="selectedCia"
                    displayField="nome"
                    valueField="id"
                    [searchFields]="['codigo', 'nome']"
                    placeholder="Selecione uma cia aérea"
                    (selectionChange)="onBulkCiaSelection($event)">
                </app-search-box>

                <div *ngIf="selectedCias.length > 0" class="chips-container">
                    <mat-chip-list>
                        <mat-chip *ngFor="let cia of selectedCias"
                                [removable]="true"
                                (removed)="removeBulkCia(cia)"
                                color="primary"
                                selected>
                            {{cia.nome}}
                            <mat-icon matChipRemove>cancel</mat-icon>
                        </mat-chip>
                    </mat-chip-list>
                </div>
            </div>

            <div class="form-section">
                <label class="section-label">3. Configuração Adicional:</label>

                <div class="form-row">

                    <app-search-box
                        formControlName="moeda"
                        [options]="[{id:'ARS', nome: 'ARS'}, {id:'BRL', nome: 'BRL'}, {id:'USD', nome: 'USD'}]"
                        displayField="nome"
                        valueField="id"
                        placeholder="Moeda"
                        valueAsValueField="true"
                        [required]="true">
                    </app-search-box>
                </div>

                <div class="form-row">
                    <app-input-field
                        type="percentage"
                        name="percentualRav"
                        label="Percentual RAV"
                        [control]="bulkFormGroup.get('percentualRav')"
                        [required]="true">
                    </app-input-field>

                    <app-input-field
                        type="monetary"
                        name="valorMinimoRav"
                        label="Valor Mínimo RAV"
                        [control]="bulkFormGroup.get('valorMinimoRav')"
                        [required]="true">
                    </app-input-field>
                </div>

                <div class="form-row">
                    <mat-checkbox formControlName="variavel">
                        Variável
                    </mat-checkbox>
                </div>
            </div>
        </form>

        <div class="modal-footer">
            <button mat-button (click)="closeBulkModal()">Cancelar</button>
            <button mat-flat-button color="primary" (click)="createBulkRecords()" [disabled]="!bulkFormGroup.valid || selectedCias.length === 0">
                Criar {{selectedCias.length}} Registro(s)
            </button>
        </div>
    </div>
</div>
